import { Config, Inject, Logger, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import {
  assign,
  divide,
  findIndex,
  flattenDeep,
  groupBy,
  isEmpty,
  isNaN,
  isNil,
  pick,
  round,
  sum,
} from 'lodash';
import { Types, UpdateQuery, UpdateWithAggregationPipeline } from 'mongoose';
import {
  MONGO_MODEL_KEY,
  QUESTION_FILTER_FIELD_MAPPING,
  SCORE_WEIGHTS,
  TRANSFORM_MAPPING_TYPE,
} from '../common/Constants';
import { CustomError } from '../error/custom.error';
import { BaseObject } from '../model/reference_type';

import { DocumentType } from '@typegoose/typegoose/lib/types';
import { FilterQuery } from 'mongoose';
import { MongoModelFactory, MongoModelKey } from '../common/MongoModelFactory';
import { TextbookCatalogService } from './textbook_catalog.service';
import { BaseMongoService } from '../common/BaseMongoService';
import { Order, Page, Projection } from '../interface';
import { load } from 'cheerio';
import { Op } from 'sequelize';
import { SubjectDictionaryService } from './subject_dictionary.service';
import { DictionaryService } from './dictionary.service';
import { QuestionExtendTypeService } from './question_extend_type.service';
import { Util } from '../common/Util';
import { HttpService } from '@midwayjs/axios';
import { KnowledgePointService } from './knowledge_point.service';
import { QuestionClassificationService } from './question_classification.service';
import { UserService } from './user.service';
import { Enterprise } from '../entity/enterprise.entity';
import { QuestionTierService } from './question_tier.service';
import { QuestionTier } from '../entity';
import { TextbookCatalogKnowledgePointService } from './textbook_catalog_knowledge_point.service';
import { UserQuestionTags } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
@Provide()
export class QuestionService extends BaseMongoService {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  mongoModelFactory: MongoModelFactory<any>;

  @Inject()
  textbookCatalogService: TextbookCatalogService;

  @Inject()
  catalogKnowledgeService: TextbookCatalogKnowledgePointService;

  @Inject()
  subjectDictionaryService: SubjectDictionaryService;
  @Inject()
  dictionaryService: DictionaryService;

  @Inject()
  questionExtendTypeService: QuestionExtendTypeService;

  @Inject()
  knowledgePointService: KnowledgePointService;

  @Inject()
  questionClassificationService: QuestionClassificationService;

  @Inject()
  questionTierService: QuestionTierService;

  @Inject()
  httpService: HttpService;

  @Inject()
  userService: UserService;

  @Config('fileTransform')
  transformConfig: any;

  @InjectEntityModel(UserQuestionTags)
  userQuestionTags: ReturnModelType<typeof UserQuestionTags>;

  /**************************************************** 以下是私有的辅助方法****************************************/
  /**
   * 构造父子题 子题的数据结构
   * @param parentInfo 父题数据
   * @param children 子题数据
   * @returns
   */
  private async _buildChildrenQuestion(parentInfo, children: any[]) {
    const commonInfo = pick(parentInfo, [
      'difficulty',
      'grade',
      'textbookVersion',
      'gradeSection',
      'subject',
      'unit',
      'period',
      'catalog',
      'author',
      'source',
      'volume',
      'tier',
      'year',
      'cognitiveHierarchy',
      'coreQuality',
      'investigationAbility',
      'tableName',
      'isShared',
      'checkStatus',
    ]);
    const childQuestions = children.map(item => {
      item.pid = parentInfo._id;
      item.parentStem = parentInfo.name;
      return assign(item, commonInfo);
    });
    return childQuestions;
  }

  /**
   * 根据条件分组统计题目数量及占比
   * @param questions 试题数组
   * @param key 分组字段
   */
  private async _groupQuestionsByFilter(
    questions: Array<{ difficulty: BaseObject; type: BaseObject }>,
    key: string
  ): Promise<{ name: string; count: number; radio: number }[]> {
    const groupObj = groupBy(questions, key);
    const documents = [];
    for (const key in groupObj) {
      const data = groupObj[key];
      const count = data.length;
      const rate = round((count / questions.length) * 100, 2);
      const name = key === 'undefined' || isNil(key) ? '未知' : key;
      documents.push({
        name,
        count,
        rate,
      });
    }
    return documents;
  }

  /**
   * 构造试题查询基础聚合条件的方法
   * @param params 查询条件参数
   */
  private async _buildQuestionPipes(params: {
    query: any;
    order?: Order;
    page?: Page;
    projection?: Projection;
    collectionName?: string;
  }) {
    const { query, order, page, projection, collectionName } = params;
    if (!query) {
      throw new CustomError('未指定查询条件！');
    }
    const { ids, idArray, catalogIds, catalog, userId, userTag, ...queryInfo } =
      query;
    if (ids && ids.length) {
      // 排除掉当前作业中的试题
      queryInfo._id = { $nin: ids.map(v => new Types.ObjectId(v)) };
    }
    if (idArray && idArray.length) {
      queryInfo._id = { $in: idArray.map(v => new Types.ObjectId(v)) };
    }
    // 仅查询父级题目
    queryInfo.pid = null;
    // 根据教材目录ids 或者 当前目录id获取子节点id信息
    if (catalogIds || catalog) {
      const parentIds = catalogIds || [catalog];
      // 根据父节点id数组获取目录下子节点的id信息
      const catalogIdArr = await this.textbookCatalogService.getAllChildren(
        parentIds.map((p: string | number) => Number(p))
      );
      queryInfo['catalog.id'] = { $in: catalogIdArr };
    }

    // 遍历其他参数，将基础字段查询转为 嵌套查询
    for (const key in queryInfo) {
      const value = queryInfo[key];
      delete queryInfo[key];
      if (QUESTION_FILTER_FIELD_MAPPING[key]) {
        const mappingField = QUESTION_FILTER_FIELD_MAPPING[key];
        const { getKey, transform } = TRANSFORM_MAPPING_TYPE[mappingField];
        queryInfo[getKey(key)] = transform(value);
      } else {
        queryInfo[key] = value;
      }
    }
    // 根据当前用户id和标记名获取对应试题id 组装到查询参数中，用于获取除查询的标记外试题其余标记
    let questionIds = [];
    if (userId && userTag) {
      const userQuestionTags = await this.userQuestionTags.find({
        userId: Number(userId), //将用户id强转为数字
        tags: {
          $regex: userTag,
        },
      });
      if (userQuestionTags.length > 0) {
        questionIds = userQuestionTags.map(
          item => new Types.ObjectId(item.questionId)
        );
        queryInfo._id = { $in: questionIds };
      }
    }
    // 基础查询条件
    const pipes: any[] = [
      {
        $match: queryInfo,
      },
    ];
    // 关联查询子题
    if (collectionName) {
      pipes.push({
        $lookup: {
          from: collectionName,
          localField: '_id',
          foreignField: 'pid',
          as: 'children',
        },
      });
    }

    // 关联查询出用户对试题的标记
    if (userId) {
      pipes.push({
        $lookup: {
          from: 'user_question_tags',
          let: { questionId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: [{ $toString: '$$questionId' }, '$questionId'] },
                userId: Number(userId), //将用户id强转为数字
              },
            },
          ],
          as: 'user_tags',
        },
      });
      // 将用户标签数据提取到试题中，方便后续使用
      pipes.push({
        $addFields: {
          user_tags: {
            $cond: [
              { $isArray: '$user_tags' },
              {
                $reduce: {
                  input: '$user_tags',
                  initialValue: [],
                  in: {
                    $concatArrays: ['$$value', '$$this.tags'],
                  },
                },
              },
              [],
            ],
          },
        },
      });
    }
    if (userTag) {
      // 如果根据用户和标记名没查出对应试题，则加入该参数查询试题
      if (questionIds && questionIds.length === 0) {
        pipes.push(
          {
            $addFields: {
              user_tags: {
                $filter: {
                  input: '$user_tags',
                  as: 'tag',
                  cond: {
                    $ne: [
                      {
                        $indexOfBytes: [
                          { $toLower: '$$tag' },
                          { $toLower: userTag },
                        ],
                      },
                      -1,
                    ],
                  },
                },
              },
            },
          },
          {
            $match: { user_tags: { $ne: [] } },
          }
        );
      }
      // 添加分页 + 总数
      pipes.push({
        $facet: {
          totalCount: [{ $count: 'total' }],
          data: [
            { $skip: Number(page.offset) || 0 },
            { $limit: Number(page.limit) || 10 },
          ],
        },
      });
    }
    // 排序
    if (order) {
      pipes.push({
        $sort: {
          ...order,
        },
      });
    }
    // 字段筛选
    if (projection) {
      pipes.push({
        $project: projection,
      });
    }
    // 分页
    if (page && !userTag) {
      pipes.push(
        {
          $skip: Number(page.offset) || 0,
        },
        {
          $limit: Number(page.limit) || 10,
        }
      );
    }
    return pipes;
  }

  /************************************************* 以下是试题查询通用接口 ***************************************/
  /**
   * 根据id获取数据
   * @param {string} id 试题id
   */
  async showQuestion(id: string, modelKey: string = MONGO_MODEL_KEY.SYSTEM) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const document = await CurrentModel.findById(id).lean().exec();
    if (!document) {
      throw new CustomError('未找到数据！');
    }
    // 如果是父子题 查找子题
    if (document.isCompose) {
      const children = await CurrentModel.find({
        pid: document._id,
      });
      return assign(document, { children });
    }
    return document;
  }
  /**
   * 查询试题列表
   * @param questions
   * @param query
   * @returns
   */
  async findQuestions(
    questions: Array<{ question_id: string; source_table: string }>,
    query?: {
      enterpriseCode?: string;
      userId?: string;
      [key: string]: any;
    }
  ) {
    const validQuestions = questions.filter(item => item.question_id);
    // 根据试题来源类型分组
    const groupObj = groupBy(validQuestions, 'source_table');
    const promises = [];
    let queryOption = {};
    let queryEnterpriseCode;
    let queryUserId;
    if (query) {
      const { enterpriseCode, userId, ...other } = query;
      queryOption = {
        ...other,
      };
      queryEnterpriseCode = enterpriseCode;
      queryUserId = userId;
    }
    // 遍历分组 分别构造查询条件
    for (const key in groupObj) {
      const questions = groupObj[key];
      const modelKey =
        key === 'undefined' || isNil(key) ? MONGO_MODEL_KEY.SYSTEM : key;
      const ids = questions.map(item => new Types.ObjectId(item.question_id));
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const collectionName = CurrentModel.collection.name;
      const match: any = { _id: { $in: ids }, ...queryOption };
      switch (modelKey) {
        case MONGO_MODEL_KEY.SCHOOL:
          if (queryEnterpriseCode) match.enterpriseCode = queryEnterpriseCode;
          break;
        case MONGO_MODEL_KEY.PERSONAL:
          if (queryUserId) match.author = queryUserId;
          break;
      }
      const pipes = [
        {
          $match: match,
        },
        {
          $lookup: {
            from: collectionName,
            localField: '_id',
            foreignField: 'pid',
            as: 'children',
          },
        },
      ];
      promises.push(CurrentModel.aggregate(pipes).exec());
    }
    // 批量查询提交
    const results = await Promise.all(promises);
    // 数组展开
    const flatArr = flattenDeep(results);
    if (isEmpty(flatArr)) return flatArr;
    // 题目排序
    const idToQuestionMap = new Map(
      flatArr.map(item => [item._id.toString(), item])
    );
    const sortedQuestions = questions.map(item =>
      idToQuestionMap.get(item.question_id)
    );
    // 过滤掉已经被删除的题
    return sortedQuestions.filter(item => !!item);
  }

  /**
   * 分页获取题库试题列表
   * @param query 查询条件
   * @param offset 偏移量
   * @param limit 每页数量
   * @param order 排序
   * @returns
   */
  async findQuestionsByFilter(
    query: FilterQuery<DocumentType<any>> = {},
    offset: number,
    limit: number,
    order: Order = {
      createdAt: -1,
    },
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const collectionName = CurrentModel.collection.name;
      const noPaging =
        (isNil(offset) || isNaN(offset)) && (isNil(limit) || isNaN(limit));
      const pipes = await this._buildQuestionPipes({
        query,
        order,
        page: noPaging
          ? null
          : {
              offset,
              limit,
            },
        collectionName,
      });
      if (noPaging) {
        const list = await CurrentModel.aggregate(pipes).exec();
        return {
          list,
        };
      }
      const match = pipes[0].$match || pipes[0];
      const [total, list] = await Promise.all([
        CurrentModel.countDocuments(match).exec(),
        CurrentModel.aggregate(pipes).exec(),
      ]);
      // 如果根据标记查询，则试题总数和试题列表需要单独处理
      if (query.userTag) {
        const total = list[0]?.totalCount[0]?.total || 0;
        const data = list[0]?.data || [];
        return { total, list: data };
      }
      return { total, list };
    } catch (error) {
      this.logger.error(
        `Failed to findQuestionsByFilter,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('查询试题失败，请稍后重试！');
    }
  }

  /**
   * 创建父子题 父子题分别为独立的题
   * @param info 父子题数据
   * @returns
   */
  async createAssociateQuestion(
    info: any,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const { children, ...data } = info;
      const pid = new Types.ObjectId()._id;
      const parentQuestion = {
        _id: pid,
        ...data,
      };
      const childQuestions = await this._buildChildrenQuestion(
        parentQuestion,
        children
      );
      return await CurrentModel.insertMany([parentQuestion, ...childQuestions]);
    } catch (error) {
      this.logger.error(
        `Failed to createAssociateQuestion,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('新增试题失败，请稍后重试！');
    }
  }

  /**
   * 编辑父子题
   * @param {Array[]} data 子题数据
   * @param {string} pid 父题id
   * @return {any} 更新结果
   */
  async updateAssociateQuestion(
    info,
    pid: string,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const { children, ...data } = info;
      // 更新父题
      // await CurrentModel.updateOne(
      //   { _id: new Types.ObjectId(pid) },
      //   { $set: data }
      // );
      // 如果是共享试题，则不用更新试题更新字段
      if (data.checkStatus || data.commitAt) {
        await CurrentModel.updateOne(
          { _id: new Types.ObjectId(pid) },
          { $set: data },
          { timestamps: false } //不更新时间戳，避免共享时 更新试题更新时间字段
        );
      } else {
        await CurrentModel.updateOne(
          { _id: new Types.ObjectId(pid) },
          { $set: data }
        );
      }
      // 查询子题
      const ques = await CurrentModel.find({ pid }).lean().exec();
      const parent = await CurrentModel.findById(pid).lean().exec();
      // 完整子题数据 覆盖旧数据
      const childrenInfoArr = children.length
        ? children.map(c => {
            const oldChild = ques.find(
              q => q._id?.toString() === c._id?.toString()
            );
            if (oldChild) {
              return assign(oldChild, c);
            }
            return c;
          })
        : JSON.parse(JSON.stringify(ques));
      const insertArr = await this._buildChildrenQuestion(
        parent,
        childrenInfoArr
      );
      // 获得要删除的题目
      const delIds = ques
        .map(q => {
          const id = q._id.toString();
          const index = findIndex(insertArr, ['_id', id]);
          return index === -1 ? id : null;
        })
        .filter(i => i);
      // 删除子题
      if (delIds.length) {
        await CurrentModel.deleteMany({
          _id: { $in: delIds.map(d => new Types.ObjectId(d)) },
        }).exec();
      }

      // 批量操作
      const bulk = insertArr.map(data => {
        if (!data._id) {
          return {
            insertOne: {
              document: data,
            },
          };
        }
        const id = new Types.ObjectId(data._id);
        return {
          updateOne: {
            filter: { _id: id },
            update: { $set: data },
            upsert: true,
          },
        };
      });
      return await CurrentModel.bulkWrite(bulk);
    } catch (error) {
      this.logger.error(
        `Failed to updateAssociateQuestion,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('编辑试题失败，请稍后重试！');
    }
  }

  /**
   * 删除父子题
   * @param pid 父题id
   */
  async deleteQuestionByPid(
    pid: string,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const questionId = new Types.ObjectId(pid);
      await CurrentModel.deleteMany({
        $or: [{ pid: questionId }, { _id: questionId }],
      }).exec();
    } catch (error) {
      this.logger.error(
        `Failed to updateAssociateQuestion,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('删除父子题失败，请稍后重试！');
    }
  }
  /**
   * 分析试题数据
   * @param questions 试题数据
   * @param query 额外查询条件
   */
  async analysisQuestions(
    questions: Array<{ question_id: string; source_table: string }>,
    type = 'preview',
    query?: {
      enterpriseCode?: string;
      userId?: string;
      [key: string]: any;
    }
  ) {
    try {
      const validQuestions = questions.filter(item => item.question_id);
      const isComposer = type === 'preview';
      // 根据试题来源类型分组
      const groupObj = groupBy(validQuestions, 'source_table');
      const promises = [];
      let queryOption = {};
      let queryEnterpriseCode;
      let queryUserId;
      if (query) {
        const { enterpriseCode, userId, ...other } = query;
        queryOption = {
          ...other,
        };
        queryEnterpriseCode = enterpriseCode;
        queryUserId = userId;
      }
      // 遍历分组 分别构造查询条件
      for (const key in groupObj) {
        const questions = groupObj[key];
        const modelKey =
          key === 'undefined' || isNil(key) ? MONGO_MODEL_KEY.SYSTEM : key;
        const ids = questions.map(item => new Types.ObjectId(item.question_id));
        const CurrentModel = this.mongoModelFactory.getModel(modelKey);
        const match: any = { _id: { $in: ids }, ...queryOption };
        switch (modelKey) {
          case MONGO_MODEL_KEY.SCHOOL:
            if (queryEnterpriseCode) match.enterpriseCode = queryEnterpriseCode;
            break;
          case MONGO_MODEL_KEY.PERSONAL:
            if (queryUserId) match.author = queryUserId;
            break;
        }
        const project = isComposer
          ? {
              difficulty: 1,
              type: 1,
            }
          : {
              difficulty: 1,
              type: 1,
              subject: 1,
              catalog: 1,
              points: 1,
              cognitiveHierarchy: 1,
              duration: 1,
            };
        const pipes = [
          {
            $match: match,
          },
          {
            $project: project,
          },
        ];
        promises.push(CurrentModel.aggregate(pipes).exec());
      }
      // 批量查询提交
      const results = await Promise.all(promises);
      // 数组展开
      const flatArr = flattenDeep(results);
      // 分组统计
      // 根据难度分组 计算数量与占比
      const difficulty = await this._groupQuestionsByFilter(
        flatArr,
        'difficulty.name'
      );
      // 根据题型分组 计算数量与占比
      const typeRes = await this._groupQuestionsByFilter(flatArr, 'type.name');
      // 如果类型是 analysis 则需要计算试题信息的覆盖度
      if (!isComposer) {
        // 计算试题信息的覆盖度
        const res = await this.calculateCoverageAndDuration(flatArr);
        const score = await this.calculateEvaluationScore(res);
        return {
          difficulty,
          type: typeRes,
          score,
          ...res,
        };
      }
      return {
        difficulty,
        type: typeRes,
      };
    } catch (error) {
      this.logger.error(
        `Failed to analysisQuestions,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('获取试题分析数据失败，请稍后重试！');
    }
  }

  /**
   * 获取可替换的试题
   * @param {*} queryObj 查询条件
   * @param {*} modelKey 试题来源类型
   */
  async findReplaceQuestions(
    queryObj: any,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const { num, ...query } = queryObj;
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const collectionName = CurrentModel.collection.name;
      const pipes = await this._buildQuestionPipes({
        query,
        projection: {
          analysis: 1, //解析
          options: 1, //选项
          parentStem: 1, //父题干
          period: 1, //课时
          catalog: 1, //最底层节点
          name: 1, //名称
          difficulty: 1, //试题难度
          pid: 1, //父id
          answer: 1, //答案
          source: 1, //来源
          baseType: 1, //基础题型
          type: 1, //扩展题型
          points: 1, //知识点
          tags: 1, //类题标签
          tableName: 1, //试题来源表
          duration: 1, // 试题时长
        },
        collectionName,
      });
      pipes.push({ $sample: { size: num } });
      const finalRes = await CurrentModel.aggregate(pipes).allowDiskUse(true);
      return { list: finalRes };
    } catch (error) {
      this.logger.error(
        `Failed to findReplaceQuestions,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('获取可替换试题失败，请稍后重试！');
    }
  }

  /**
   * 条件查询试题数量及类型
   * @param questions
   * @param query
   * @returns
   */
  async findQuestionCountByFilter(
    query: FilterQuery<DocumentType<any>> = {},
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const { catalog } = query;
      if (!catalog) {
        throw new CustomError('未指定目录信息！');
      }
      const pipes = await this._buildQuestionPipes({
        query,
        order: {
          createdAt: -1,
        },
        projection: {
          _id: 1,
          type: {
            name: '$type.name',
            code: '$type.code',
          },
          difficulty: {
            name: '$difficulty.name',
            code: '$difficulty.code',
          },
        },
      });
      pipes.push(
        {
          $group: {
            _id: {
              type: '$type', // 按照 type 字段分组
              difficulty: '$difficulty', // 同时按照 difficulty 字段分组
            },
            count: { $sum: 1 }, // 统计每组的文档数量
          },
        },
        {
          $project: {
            _id: 0, // 去掉 _id 字段
            typeName: '$_id.type.name',
            typeCode: '$_id.type.code',
            difficultyName: '$_id.difficulty.name',
            difficultyCode: '$_id.difficulty.code',
            count: 1,
          },
        }
      );
      const list = await CurrentModel.aggregate(pipes).exec();
      return list;
    } catch (error) {
      this.logger.error(
        `Failed to findQuestionCountByFilter,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('获取试题数据失败，请稍后重试！');
    }
  }

  /**
   * 批量更新试题
   * @param idInfoArr
   * @param updateData
   * @param modelKey
   * @returns
   */
  async bulkUpdateQuestions(
    ids: string[],
    updateData: UpdateQuery<any> | UpdateWithAggregationPipeline | any,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const { commitAt } = updateData;
      if (commitAt) {
        updateData.commitAt = new Date();
      }
      const searchIds = ids.map(i => new Types.ObjectId(i));
      const filter = {
        $or: [
          {
            _id: {
              $in: searchIds,
            },
          },
          {
            pid: {
              $in: searchIds,
            },
          },
        ],
      };
      // const document = await CurrentModel.updateMany(filter, {
      //   $set: updateData,
      // }).exec();
      let document;
      // 如果是批量共享试题，则不更新对应更新时长
      if (updateData.checkStatus || updateData.commitAt) {
        document = await CurrentModel.updateMany(
          filter,
          { $set: updateData },
          { timestamps: false }
        ).exec();
      } else {
        document = await CurrentModel.updateMany(filter, {
          $set: updateData,
        }).exec();
      }
      return document;
    } catch (error) {
      this.logger.error(
        `Failed to bulkUpdateQuestions,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('批量更新试题失败，请稍后重试！');
    }
  }

  /**
   * 批量删除试题
   * @param ids 试题id数组
   * @param modelKey
   */
  async bulkDestroyQuestions(
    ids: string[],
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    try {
      const CurrentModel = this.mongoModelFactory.getModel(modelKey);
      const filter = {
        $or: [
          {
            _id: {
              $in: ids.map(i => new Types.ObjectId(i)),
            },
          },
          {
            pid: {
              $in: ids.map(i => new Types.ObjectId(i)),
            },
          },
        ],
      };
      await CurrentModel.deleteMany(filter);
    } catch (error) {
      this.logger.error(
        `Failed to bulkDestroyQuestions,message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('批量删除试题失败，请稍后重试！');
    }
  }

  /**
   * 补全试题数据
   */
  async completeQuestionStem(type: any) {
    const CurrentModel = this.mongoModelFactory.getModel(type);
    const allQuestions = await this.findByFilter(
      {
        $or: [
          {
            stem: { $exists: false },
          },
          { stem: null },
        ],
      },
      type
    );
    const bulk = allQuestions.map(question => {
      const { name } = question;
      const $ = load(name);
      // 移除所有 img 标签
      $('img').remove();

      // 获取 body 中的纯文本并清理空白字符
      const text = $('body').text().trim().replace(/\s+/g, '');
      return {
        updateOne: {
          filter: { _id: question._id },
          update: { $set: { stem: text || null } },
          upsert: true,
        },
      };
    });
    // 批量更新
    return await CurrentModel.bulkWrite(bulk);
  }

  /**
   * 计算试题的覆盖度
   * @param questions 试题数据
   * @returns
   */
  async calculateCoverageAndDuration(questions: any[]) {
    // 试题的数据
    try {
      const questionPoints = [];
      const questionTypes = [];
      const questionDifficulties = [];
      const questionCognitiveHierarchy = [];
      const catalogs = [];
      const subjectIds = [];
      const durations = [];
      for (const question of questions) {
        if (question.points?.length) {
          questionPoints.push(...question.points.map(i => i.id));
        }
        if (question.type?.code) {
          questionTypes.push(question.type.code);
        }
        if (question.difficulty?.code) {
          questionDifficulties.push(question.difficulty.code);
        }
        if (question.cognitiveHierarchy?.id) {
          questionCognitiveHierarchy.push(question.cognitiveHierarchy.id);
        }
        if (question.catalog?.id) {
          catalogs.push(question.catalog.id);
        }
        if (question.subject?.id) {
          subjectIds.push(question.subject.id);
        }
        durations.push(question.duration || 0);
      }

      // 去重
      const uniqueQuestionPoints = Util.uniqArray(questionPoints);
      const uniqueQuestionTypes = Util.uniqArray(questionTypes);
      const uniqueQuestionDifficulties = Util.uniqArray(questionDifficulties);
      const uniqueQuestionCognitiveHierarchy = Util.uniqArray(
        questionCognitiveHierarchy
      );

      // 并发获取数据 汇总试题的知识点与章节目录的知识点、扩展题型、难度、认知层次
      const [
        allPoints,
        { list: allSubjectDictionaries },
        { list: allTypes },
        { list: allDifficulties },
      ] = await Promise.all([
        this.catalogKnowledgeService.getListByCatalogIds(catalogs),
        this.subjectDictionaryService.findAll({
          query: { type: '认知层次', subjectId: { [Op.in]: subjectIds } },
          attributes: ['id', 'name'],
        }),
        this.questionExtendTypeService.findAll({
          query: { subjectId: { [Op.in]: subjectIds } },
          attributes: ['id', 'code', 'name'],
        }),
        this.dictionaryService.findAll({
          query: { type: 'difficulty_type' },
          attributes: ['code', 'name'],
        }),
      ]);

      // 提取所有知识点ID
      const totalPointCount = Util.uniqArray(allPoints).length;
      // 提取所有扩展题型code
      // 提取所有难度code
      // 提取所有认知层次id
      const totalTypeCount = Util.uniqArray(
        allTypes.map(type => type.code)
      ).length;
      const totalDifficultyCount = Util.uniqArray(
        allDifficulties.map(difficulty => difficulty.code)
      ).length;
      const totalCognitiveHierarchyCount = Util.uniqArray(
        allSubjectDictionaries.map(dictionary => dictionary.id)
      ).length;

      // 覆盖率计算
      return {
        pointCoverage: totalPointCount
          ? round((uniqueQuestionPoints.length / totalPointCount) * 100, 2)
          : 0,
        typeCoverage: totalTypeCount
          ? round((uniqueQuestionTypes.length / totalTypeCount) * 100, 2)
          : 0,
        difficultyCoverage: totalDifficultyCount
          ? round(
              (uniqueQuestionDifficulties.length / totalDifficultyCount) * 100,
              2
            )
          : 0,
        cognitiveHierarchyCoverage: totalCognitiveHierarchyCount
          ? round(
              (uniqueQuestionCognitiveHierarchy.length /
                totalCognitiveHierarchyCount) *
                100,
              2
            )
          : 0,
        duration: sum(durations),
      };
    } catch (error) {
      this.logger.error(
        `Failed to calculateCoverage, message is ${error.message}, stack is ${error.stack}`
      );
      throw new CustomError('分析试题信息失败，请稍后重试！');
    }
  }

  /**
   * 计算作业评价分
   * @param info
   * @returns
   */
  async calculateEvaluationScore(info: {
    pointCoverage: number;
    typeCoverage: number;
    cognitiveHierarchyCoverage: number;
    difficultyCoverage: number;
    duration?: number;
  }) {
    // 按照计分规则进行加权计算 计算出作业评价分
    const totalScore = 100;
    const rawScore =
      info.pointCoverage * SCORE_WEIGHTS.point * totalScore +
      info.typeCoverage * SCORE_WEIGHTS.type * totalScore +
      info.difficultyCoverage * SCORE_WEIGHTS.difficulty * totalScore +
      info.cognitiveHierarchyCoverage *
        SCORE_WEIGHTS.cognitiveHierarchy *
        totalScore;
    const score = round(divide(rawScore, 100), 2);
    return Math.max(0, Math.min(score, totalScore));
  }
  /**
   * 上传并导入试题
   * @param filePath 文件临时地址
   * @param questionInfo 导入试题的基础信息（如学科、教材版本等）
   */
  async importQuestions(filePath: string, questionInfo: any): Promise<any> {
    try {
      // 1. 验证用户登录信息
      const user = this.ctx.state?.user;
      if (!user) {
        throw new CustomError('缺少用户登录信息！');
      }
      // 获取用户的企业信息
      const userInfo = await this.userService.findAll({
        query: { id: user.id },
        include: [{ model: Enterprise, as: 'enterprise' }],
      });
      const currentUser = userInfo?.list[0];
      if (!currentUser) {
        throw new CustomError('缺少用户登录信息！');
      }
      const { enterprise } = currentUser;

      // 2. 获取解析后的试题数据
      const parseRes = await this.fetchParsedQuestions(filePath);
      const { status, data } = parseRes;
      if (status !== 200) {
        throw new CustomError('试题上传解析失败，请检查试题文件！');
      }
      // 3. 提取 subjectId 并获取相关参考数据
      const { tableName, subject } = questionInfo;
      if (!subject || !subject?.id) {
        throw new CustomError('试题导入失败，未指定学科信息！');
      }

      // 4. 获取字典与关联数据
      const referenceData = await this.fetchReferenceData(
        subject.id,
        enterprise?.code
      );

      // 5. 构建映射 Map
      const combinedMap = this.buildCombinedMap(referenceData);

      // 6. 构造最终要插入的数据
      const questions = this.constructQuestions(
        data.questions,
        combinedMap,
        questionInfo,
        {
          id: currentUser.id,
          name: currentUser.nickname,
          enterpriseCode: enterprise?.code,
        }
      );
      // 7. 验证数据
      const validQuestions = this.validQuestions(questions);

      // 8. 分批次插入数据
      const insertRes = await this.insertMany(validQuestions, tableName);
      // 9. 构造返回结果
      const result = JSON.parse(JSON.stringify(insertRes)).map(item => ({
        _id: item._id,
        pid: item.pid,
        tableName: item.tableName,
      }));
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to import questions: ${error.message}, stack: ${error.stack}`
      );
      throw new CustomError(`试题导入失败，${error.message}`);
    }
  }

  /**
   * 获取解析后的试题内容
   */
  private async fetchParsedQuestions(filePath: string): Promise<any> {
    try {
      const { baseURL, parseWord } = this.transformConfig;
      return await this.httpService.post(`${baseURL}${parseWord}`, {
        filePath,
      });
    } catch (error) {
      this.logger.error(`试题解析失败: ${error.message}`);
      throw new CustomError('试题文件解析失败，请检查试题解析服务或稍后重试！');
    }
  }

  /**
   * 获取所有关联的参考数据（字典、扩展题型、知识点等）
   */
  private async fetchReferenceData(
    subjectId: number,
    enterpriseCode: string
  ): Promise<any> {
    try {
      const [
        { list: dictionaries },
        { list: extendTypes },
        { list: knowledgePoints },
        { list: questionClassifications },
        { list: questionTiers },
        { list: subjectDictionaries },
      ] = await Promise.all([
        // 字典表：难度
        this.dictionaryService.findAll({
          query: { type: 'difficulty_type' },
          attributes: ['code', 'name', 'type'],
        }),
        // 扩展题型
        this.questionExtendTypeService.findAll({
          query: { subjectId },
          attributes: ['code', 'name', 'sourceCode', 'sourceName'],
        }),
        // 知识点
        this.knowledgePointService.findAll({
          query: { subjectId },
          attributes: ['id', 'name'],
        }),
        // 试题分类
        this.questionClassificationService.findAll({
          query: enterpriseCode
            ? { [Op.or]: [{ enterpriseCode }, { isBuiltIn: true }] }
            : {
                isBuiltIn: true,
              },
          attributes: ['id', 'name'],
        }),
        // 试题分层
        this.questionTierService.findAll({
          query: enterpriseCode
            ? { [Op.or]: [{ enterpriseCode }, { isBuiltIn: true }] }
            : {
                isBuiltIn: true,
              },
          attributes: ['id', 'name'],
        }),
        // 学科关联数据：认知层次、核心素养、考察能力
        this.subjectDictionaryService.findAll({
          query: { subjectId },
          attributes: ['id', 'name', 'type'],
        }),
      ]);

      return {
        dictionaries,
        extendTypes,
        knowledgePoints,
        questionClassifications,
        questionTiers,
        subjectDictionaries,
      };
    } catch (error) {
      this.logger.error(`获取参考数据失败: ${error.message}`);
      throw new CustomError('获取参考数据失败，请稍后重试！');
    }
  }

  /**
   * 构建组合映射 Map
   */
  private buildCombinedMap(data: any): Map<string, any> {
    const combinedMap = new Map<
      string,
      {
        id?: number;
        code?: string;
        name: string;
        sourceCode?: string;
        sourceName?: string;
      }
    >();

    function addToMap(
      array: Array<{
        id?: number;
        code?: string;
        name: string;
        type?: string;
        sourceCode?: string;
        sourceName?: string;
      }>,
      keyPrefix = ''
    ) {
      for (const item of array) {
        const key = item.type
          ? `${item.type}:${item.name}`
          : `${keyPrefix}:${item.name}`;
        combinedMap.set(key, {
          id: item.id,
          code: item.code,
          name: item.name,
          sourceCode: item.sourceCode,
          sourceName: item.sourceName,
        });
      }
    }

    addToMap(data.dictionaries);
    addToMap(data.extendTypes, 'extendType');
    addToMap(data.knowledgePoints, 'point');
    addToMap(data.questionClassifications, 'classification');
    addToMap(data.questionTiers, 'tier');
    addToMap(data.subjectDictionaries);

    return combinedMap;
  }

  /**
   * 构造最终要插入的试题数据
   */
  private constructQuestions(
    parsedData: any[],
    combinedMap: Map<string, any>,
    questionInfo: any,
    userInfo: any
  ): any[] {
    const questions = [];
    const { id, name, enterpriseCode } = userInfo;
    for (const q of parsedData) {
      const {
        content,
        options,
        answer,
        explanation,
        pagination,
        difficulty,
        level,
        type,
        knowledgePoints,
        coreQuality,
        cognitiveHierarchy,
        investigationAbility,
      } = q;
      const $ = load(content);
      const hasImage = $('img').length > 0;
      const stem = hasImage
        ? null
        : $('body').text().trim().replace(/\s+/g, '');

      const extendType = combinedMap.get(`extendType:${type}`);
      const newQuestion = {
        ...questionInfo,
        name: content,
        stem,
        options,
        answer,
        analysis: explanation,
        pagination,
        isImported: true,
        baseType: extendType
          ? { code: extendType.sourceCode, name: extendType.sourceName }
          : null,
        type: extendType,
        difficulty: combinedMap.get(`difficulty_type:${difficulty}`),
        classification: combinedMap.get(`classification:${type}`),
        points: isEmpty(knowledgePoints)
          ? []
          : knowledgePoints
              .map(point => combinedMap.get(`point:${point}`))
              .filter(i => i),
        coreQuality: combinedMap.get(`核心素养:${coreQuality}`),
        cognitiveHierarchy: combinedMap.get(`认知层次:${cognitiveHierarchy}`),
        investigationAbility: combinedMap.get(
          `考察能力:${investigationAbility}`
        ),
        tier: combinedMap.get(`tier:${level}`),
        author: { id, name },
        enterpriseCode,
        year: new Date().getFullYear(),
      };
      questions.push(newQuestion);
    }

    return questions;
  }

  /**
   * 校验试题数据
   * @param questions 试题数据
   * @returns
   */
  private validQuestions(questions: any[]) {
    const requiredFields = [
      {
        key: 'name',
        label: '题干',
      },
      {
        key: 'type',
        label: '题型',
      },
    ]; // 必须字段
    const fieldErrorMap = {};
    const invalidQuestions = [];
    const validQuestions = [];
    for (const q of questions) {
      // 必填字段校验
      const missingFields = requiredFields.filter(field => !q[field.key]);
      if (missingFields.length > 0) {
        missingFields.forEach(field => {
          fieldErrorMap[field.label] = (fieldErrorMap[field.label] || 0) + 1;
        });
        invalidQuestions.push(q);
        continue;
      }
      validQuestions.push(q);
    }
    if (invalidQuestions.length > 0) {
      const errorMessages = Object.entries(fieldErrorMap).map(
        ([field, count]) =>
          `【${field}】字段缺失或在系统中不存在，共 ${count} 处！`
      );
      throw new CustomError(`${errorMessages.join('; ')}`);
    }
    return validQuestions;
  }

  /**
   * 更新试题的分层 将对应 code 改为 id
   * @param type 模型类型
   * @returns
   */
  async changeQuestionTier(type: any) {
    // 获取所有的试题分层数据
    const questionTiers = await QuestionTier.findAll();
    const CurrentModel = this.mongoModelFactory.getModel(type);
    // 获取所有存在分层数据的试题
    const allQuestions = await this.findByFilter(
      {
        $or: [{ tier: { $exists: 1 } }],
      },
      type
    );
    const bulk = allQuestions.map(question => {
      const { _id, tier } = question;
      const set: any = {};
      if (tier) {
        const questionTier = questionTiers.find(
          item => item.name === tier.name
        );
        set.tier = questionTier
          ? {
              id: questionTier.id,
              name: questionTier.name,
            }
          : null;
      }
      return {
        updateOne: {
          filter: { _id },
          update: { $set: set },
          upsert: true,
        },
      };
    });
    // 批量更新
    return await CurrentModel.bulkWrite(bulk);
  }

  /**
   * 根据用户id获取用户定义的所有标记
   * @param userId 用户id
   */
  async getUserTag(userId) {
    const res = await this.userQuestionTags.find({ userId });
    if (!res) {
      return [];
    }
    // 获取所有标记信息，并去重合并为一个数组
    const tags = res.map(item => item.tags).flat();
    return [...new Set(tags)];
  }

  /**
   * 批量添加试题标记，以最新的为准
   * @param info 标记信息
   */
  async bulkAddUserTag(info) {
    const { userId, questionId, tags } = info;
    if (!userId) {
      throw new CustomError('用户信息不能为空');
    }
    if (!questionId) {
      throw new CustomError('试题信息不能为空');
    }
    if (tags.length > 0) {
      // 判断是否有重复项
      const hasDuplicate = new Set(tags).size < tags.length;
      if (hasDuplicate) {
        throw new CustomError('存在重复标记项');
      }
    }

    //根据用户id 和 试题id 删除对应标记
    await this.userQuestionTags.deleteMany({
      userId,
      questionId,
    });
    return await this.userQuestionTags.create(info);
  }
}
