<div
  style="
    font-family: <%= styles.fontFamily %>;
    width: <%= styles.contentWidth %>;
    height: <%= styles.contentHeight %>;
    padding-block: 66px;
    padding-inline: 47px;
    font-size: <%= styles.fontSize %>;
    line-height: <%= styles.lineHeight %>;
  "
>
  <div style="text-align: center">
    <h1><%= name %></h1>
  </div>
  <div>
    <div>
      <div></div>
      <div
        id="paperInfo"
        contenteditable="true"
        style="position: relative"
        spellcheck="false"
      >
        <div
          style="
            font-family: <%= styles.fontFamily %>;
            font-size: <%= styles.fontSize %>;
            line-height: <%= styles.lineHeight %>;
            text-align: center;
          "
        >
          姓名：__________ 班级：__________ 考号：__________
        </div>
      </div>
    </div>
  </div>

  <% infos.forEach((section, sectionIndex)=> { %> <% if(section.questions &&
  section.questions.length> 0) { %> <% section.questions.forEach((question,
  questionIndex)=> { %> <% if(questionIndex===0 && section.type !=='暂无分组' )
  { %>
  <div
    style="
      height: 40px;
      font-weight: bold;
      align-items: center;
      gap: 8px;
      white-space: nowrap;
    "
  >
    <span> <%= section.sectionTitle %>、 </span>
    <div><%= section.type %></div>
  </div>
  <% } %>
  <div style="align-items: center; white-space: nowrap">
    <span style="visibility: visible"> <%= question.questionNumber %>. </span>
    <div style="display: inline-block"><%-question.name %></div>
  </div>

  <% if(question.options && question.options.length> 0) { %>
  <div>
    <% question.options.forEach((option)=> { %> <%
    Object.keys(option).forEach(key=> { %>
    <div style="align-items: center; white-space: nowrap; gap: 8px">
      <span style="line-height: 1.5; display: inline-block"
        ><span style="padding-inline: 3px"> <%= key %>、 </span></span
      >
      <div style="display: inline-block; vertical-align: middle">
        <div style="display: inline-block"><%- option[key] %></div>
      </div>
    </div>
    <% }); %> <% }); %>
  </div>
  <% } %> <% if(question.children && question.children.length> 0) { %> <%
  question.children.forEach((child, childIndex)=> { %>
  <div style="align-items: center; gap: 8px; white-space: nowrap">
    <span style="visibility: visible; flex-shrink: 0">
      <%= childIndex + 1 %>）.
    </span>
    <div style="flex: 1; min-width: 0"><%- child.name %></div>
  </div>

  <% if(child.options && child.options.length> 0) { %>
  <div>
    <% child.options.forEach((option)=> { %> <%
    Object.keys(option).forEach(key=> { %>
    <div style="align-items: center; gap: 8px; white-space: nowrap">
      <div style="line-height: 1.5; flex-shrink: 0">
        <span style="padding-inline: 3px"> <%= key %>、 </span>
      </div>
      <div style="flex: 1; min-width: 0">
        <div>
          <div
            contenteditable="true"
            style="position: relative; display: inline-block"
            spellcheck="false"
          >
            <%- option[key] %>
          </div>
        </div>
      </div>
    </div>
    <% }); %> <% }); %>
  </div>
  <% } %> <% }); %> <% } %> <% }); %> <% } %> <% }); %>
</div>
