import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { PersonalQuestionService } from '../service/personal_question.service';
import { CustomError } from '../error/custom.error';
import { Util } from '../common/Util';
import { QuestionService } from '../service/question.service';
import { MONGO_MODEL_KEY } from '../common/Constants';

@Controller('/personal_question')
export class PersonalQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: PersonalQuestionService;

  @Inject()
  questionService: QuestionService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      order,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const sort = order ? JSON.parse(order) : order;
    return await this.questionService.findQuestionsByFilter(
      queryInfo,
      Util.toInt(offset),
      Util.toInt(limit),
      sort,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  @Get('/:id', { summary: '根据id查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.questionService.showQuestion(
      id,
      MONGO_MODEL_KEY.PERSONAL
    );
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.questionService
      .create(info, MONGO_MODEL_KEY.PERSONAL)
      .catch(() => {
        throw new CustomError('新增试题失败，请稍后重试！');
      });
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    const { commitAt } = body;
    if (commitAt) {
      body.commitAt = new Date();
    }
    // await this.questionService
    //   .update(id, body, MONGO_MODEL_KEY.PERSONAL)
    //   .catch(() => {
    //     throw new CustomError('编辑试题失败，请稍后重试！');
    //   });
    await this.service.update(id, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.questionService
      .destroy(id, MONGO_MODEL_KEY.PERSONAL)
      .catch(() => {
        throw new CustomError('删除试题失败，请稍后重试！');
      });
    return true;
  }

  @Del('/destroybulk', { summary: '批量删除' })
  async destroyBulk(@Body() body: any) {
    const { ids } = body;
    if (!ids.length) {
      throw new CustomError('未指定要删除的题目！');
    }
    await this.questionService.bulkDestroyQuestions(
      ids,
      MONGO_MODEL_KEY.PERSONAL
    );
    return true;
  }

  @Post('/updatebulk', { summary: '批量更新' })
  async updateBulk(@Body() body: any) {
    const { ids, data } = body;
    if (!ids.length) {
      throw new CustomError('未指定要删除的题目！');
    }
    return this.questionService.bulkUpdateQuestions(
      ids,
      data,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  @Post('/associate', { summary: '创建父子题' })
  async createAssociateQuestion(@Body() body: any) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    return this.questionService.createAssociateQuestion(
      body,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  @Post('/associate_update/:pid', { summary: '编辑父子题' })
  async updateAssociateQuestion(@Param('pid') pid: string, @Body() body: any) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    const { commitAt } = body;
    if (commitAt) {
      body.commitAt = new Date();
    }
    return await this.questionService.updateAssociateQuestion(
      body,
      pid,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  @Del('/associate/:pid', { summary: '根据父题id删除关联的子题' })
  async deleteQuestionByPid(@Param('pid') pid: string) {
    if (!pid) {
      throw new CustomError('未指定要删除的题目');
    }
    await this.questionService.deleteQuestionByPid(
      pid,
      MONGO_MODEL_KEY.PERSONAL
    );
    return true;
  }

  @Post('/list', { summary: '根据试题ids获取多个试题' })
  async getList(@Body() body: any) {
    const { ids } = body;
    const idsArr: any[] = [...new Set(ids)];
    const res = await this.questionService.findQuestionsByFilter(
      { idArray: idsArr },
      null,
      null,
      { commitAt: -1 },
      MONGO_MODEL_KEY.PERSONAL
    );
    return res;
  }

  @Get('/get_count', { summary: '条件查询试题个数' })
  async findQuestionCountByFilter(@Query() query: any) {
    return await this.questionService.findQuestionCountByFilter(
      query,
      MONGO_MODEL_KEY.PERSONAL
    );
  }
}
