import { Inject, Provide } from '@midwayjs/core';
import {
  AnalysisTeacherWorkRecord,
  ComplianceDetectionDesign,
  ComplianceDetectionDesignDetail,
  Dictionary,
  LessonWorkDesign,
  LessonWorkDesignDetail,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op, col, fn } from 'sequelize';

@Provide()
export class AnalysisTeacherWorkRecordService extends BaseService<AnalysisTeacherWorkRecord> {
  @Inject()
  ctx: Context;

  constructor() {
    super('教师作业分析');
  }
  getModel = () => {
    return AnalysisTeacherWorkRecord;
  };

  /**
   * 统计各教师作业情况
   * @param query 请求参数
   */
  async statistic(query: any) {
    const {
      semester_code,
      grade_section_code,
      enterprise_code,
      grade_code,
      subject_id,
    } = query;
    const queryOption = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (grade_section_code) {
      queryOption['grade_section_code'] = grade_section_code;
    }
    if (enterprise_code) {
      queryOption['enterprise_code'] = enterprise_code;
    }
    if (grade_code) {
      queryOption['grade_code'] = grade_code;
    }
    if (subject_id) {
      queryOption['subject_id'] = subject_id;
    }

    // 如果是按年级筛选，将对应年级学科的建议时长一并返回
    if (grade_code) {
      const res = await AnalysisTeacherWorkRecord.findAll({
        where: queryOption,
        attributes: [
          'teacher_id',
          'teacher_name',
          'subject_id',
          'subject',
          'suggested_time',
          // [fn('SUM', col('question_number')), 'total_question_number'], //暂时未统计教师录题数量
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
        ],
        group: [
          'teacher_id',
          'teacher_name',
          'subject_id',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    const res = await AnalysisTeacherWorkRecord.findAll({
      where: queryOption,
      attributes: [
        'teacher_id',
        'teacher_name',
        'subject_id',
        'subject',
        // 'suggested_time',
        // [fn('SUM', col('question_number')), 'total_question_number'], //暂时未统计教师录题数量
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
      ],
      group: [
        'teacher_id',
        'teacher_name',
        'subject_id',
        'subject',
        // 'suggested_time',
      ],
    });
    return res;
  }

  /**
   * 自动汇总各年级教师学科作业、平均时长
   */
  async autoSummaryTeacherWorkRecord(
    subjectSuggestionTime,
    semester,
    enterprises
  ) {
    // 从字典表获取年级信息
    const gradeList = await Dictionary.findAll({ where: { type: 'grade' } });

    // 格式化当前学年学期的时间范围 方便后续查询使用
    const startTime = new Date(semester.start_date);
    const endTime = new Date(semester.end_date);

    const recordsToCreate = []; // 需要创建的记录数组
    const recordsToUpdate = []; // 需要更新的记录数组

    for (const enterprise of enterprises) {
      if (!enterprise.school_system) {
        continue;
      }

      // 解构出教师信息
      const { users } = enterprise;
      if (!users || !users.length) {
        continue;
      }
      const userAndGrade = [];
      // 将用户和年级信息平铺展开
      users.forEach(user => {
        const { grade } = user;
        grade.forEach(gradeInfo => {
          userAndGrade.push({
            ...user.toJSON(),
            grade_name: gradeInfo,
          });
        });
      });

      // 遍历用户信息，统计用户的作业次数、作业平均时长
      for (const userInfo of userAndGrade) {
        const baseQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: enterprise.id,
          grade_section_code: userInfo.grade_section_code,
          grade_name: userInfo.grade_name,
          subject_id: userInfo.subject_id,
          creator_id: userInfo.id,
        };

        // 获取对应年级、学科 的 课时、达标范本详情
        const [workDesignDetailList, detectionDesignDetailList] =
          await Promise.all([
            LessonWorkDesignDetail.findAll({
              include: [
                { model: LessonWorkDesign, where: baseQuery, attributes: [] },
              ],
              attributes: ['id', 'duration'],
            }),
            ComplianceDetectionDesignDetail.findAll({
              include: [
                {
                  model: ComplianceDetectionDesign,
                  where: baseQuery,
                  attributes: [],
                },
              ],
              attributes: ['id', 'duration'],
            }),
          ]);

        const questionDuration = workDesignDetailList.reduce((total, item) => {
          return total + item.duration;
        }, 0);

        const detectionQuestionDuration = detectionDesignDetailList.reduce(
          (total, item) => {
            return total + item.duration;
          },
          0
        );

        // 汇总作业总时长
        const totalDuration = questionDuration + detectionQuestionDuration;

        // 汇总作业总数
        const homework_number =
          workDesignDetailList.length + detectionDesignDetailList.length;

        // 计算平均时长 (时长/作业数)
        const average_time =
          homework_number > 0 && totalDuration > 0
            ? parseFloat((totalDuration / homework_number).toFixed(2))
            : 0;

        // 过滤出对应学段、年级、科目 的建议时长
        const suggestTime = subjectSuggestionTime.find(
          item =>
            item.grade_section_code === userInfo.grade_section_code &&
            item.subject_id === userInfo.subject_id &&
            item.grade_name === userInfo.grade_name
        );
        const suggested_time = suggestTime
          ? `${suggestTime.min_duration}-${suggestTime.max_duration}`
          : '';

        // 根据年级名称 获取年级code信息
        const gradeInfo = gradeList.find(
          item => item.name === userInfo.grade_name
        );
        // 组装当前学校教师对应数据
        const teacherData = {
          semester_code: semester.code, // 学期编码
          semester_name: semester.term.toString(), // 学期名称
          grade_section_name: userInfo.grade_section_name, // 学段名称
          grade_section_code: userInfo.grade_section_code, // 学段编码
          enterprise_code: enterprise.code, // 学校编码
          enterprise_name: enterprise.name, // 学校名称
          grade_code: gradeInfo.code, // 年级编码
          grade_name: userInfo.grade_name, // 年级名称
          subject_id: userInfo.subject_id, // 科目id
          subject: userInfo.subject_name, // 科目
          teacher_id: userInfo.id, // 教师id
          teacher_name: userInfo.nickname, // 教师名称
          homework_number, // 作业次数
          average_time: average_time.toString(), // 平均时长
          suggested_time, // 建议时长
          // question_number, // 试题数量 保留字段
        };

        // 查询当前学年学期对应年级、学科、教师 作业次数是否存在
        const record = await AnalysisTeacherWorkRecord.findOne({
          where: {
            semester_code: semester.code,
            grade_section_code: userInfo.grade_section_code,
            enterprise_code: enterprise.code,
            grade_name: userInfo.grade_name,
            subject_id: userInfo.subject_id,
            teacher_id: userInfo.id,
          },
        });

        // 存在则更新，不存在则创建
        if (record) {
          recordsToUpdate.push({
            id: record.id,
            homework_number,
            average_time: average_time.toString(),
            suggested_time,
          });
        } else {
          recordsToCreate.push(teacherData);
        }
      }
    }

    // 批量创建或更新记录
    if (recordsToCreate.length) {
      await this.batchCreteOrUpdate(recordsToCreate, 'create');
    }
    if (recordsToUpdate.length) {
      await this.batchCreteOrUpdate(recordsToUpdate, 'update');
    }
  }
}
