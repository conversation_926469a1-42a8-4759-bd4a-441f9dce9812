import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComposerPaper } from './composer_paper.entity';

export interface ComposerQuestionAttributes {
  /** 试题ID */
  questionBankId: string;
  /** 组卷方案ID */
  composerPaperId: number;
  /** 试题扩展类型 */
  type: string;
  /** 试题来源类型 */
  sourceTable: string;
}

@Table({
  tableName: 'composer_question',
  timestamps: false,
  comment: '组卷试题表',
})
export class ComposerQuestion
  extends Model<ComposerQuestionAttributes>
  implements ComposerQuestionAttributes
{
  @Column({
    type: DataType.STRING(64),
    primaryKey: true,
    allowNull: false,
    comment: '试题ID',
    field: 'question_bank_id',
  })
  questionBankId: string;

  @ForeignKey(() => ComposerPaper)
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    allowNull: false,
    comment: '组卷方案ID',
    field: 'composer_paper_id',
  })
  composerPaperId: number;

  @BelongsTo(() => ComposerPaper)
  composerPaper: ComposerPaper;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试题扩展类型',
    field: 'type',
  })
  type: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题来源类型',
    field: 'source_table',
  })
  sourceTable: string;
}
