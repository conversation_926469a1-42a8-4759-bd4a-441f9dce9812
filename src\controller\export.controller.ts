import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ExportService } from '../service/export.service';
import { CustomError } from '../error/custom.error';
import { readFile } from 'fs/promises';
@Controller('/export')
export class ExportController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ExportService;

  @Post('/paper-to-word', { summary: '导出作业试卷为word文档' })
  async exportPaper(@Body() body: any) {
    const { name, struct, questions, options } = body;
    // 检查参数是否为空
    if (!name) {
      throw new CustomError('缺少作业名称！');
    }
    if (!struct || !questions) {
      throw new CustomError('缺少作业试题数据！');
    }
    const { wordFilePath, htmlFilePath } = await this.service.exportPaperToWord(
      name,
      struct,
      questions,
      options
    );
    const fileStream = readFile(wordFilePath);

    // 设置响应头
    this.ctx.set(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    this.ctx.set(
      'Content-Disposition',
      `attachment; filename=${encodeURIComponent(name)}`
    );
    this.ctx.body = fileStream;

    // 完成后删除文件
    this.ctx.res.on('finish', async () => {
      try {
        // await unlink(htmlFilePath);
      } catch (err) {
        console.error(`删除文件失败:${htmlFilePath}`, err);
      }
    });
  }
}
