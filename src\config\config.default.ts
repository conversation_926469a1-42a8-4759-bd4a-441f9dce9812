import { MidwayConfig } from '@midwayjs/core';
import { uploadWhiteList } from '@midwayjs/busboy';
import { tmpdir } from 'os';
import { join } from 'path';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1738998787427_4184',
  koa: {
    port: 3130,
  },
  redis: {
    client: {
      host: '************',
      port: 6379,
      password: 'carryredis',
      db: 6,
    },
  },
  bullmq: {
    defaultConnection: {
      host: '************',
      port: 6379,
      password: 'carryredis',
      db: 6,
    },
    // 可选，队列前缀
    defaultPrefix: '{midway-bullmq}',
  },
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'Clouddeep@8890',
        database: 'homework-design',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        sync: false,
      },
    },
  },
  mongoose: {
    dataSource: {
      default: {
        uri: 'mongodb://*************:27017/home-design',
        options: {
          useNewUrlParser: true,
          useUnifiedTopology: true,
          // user: '***********',
          // pass: '***********',
        },
        // 关联实体
        entities: ['model'],
      },
    },
  },
  // JWT配置
  jwt: {
    secret: 'oauth@2025',
    sign: {
      expiresIn: '1h',
    },
  },
  // 本地认证配置，这里的配置只对auth.service生效，目前用户注册走的其他分支，但这个配置思路很好，后期可以考虑引用
  localAuth: {
    // 是否允许用户注册
    allowRegister: false,
    // 默认角色（新注册用户的角色）
    defaultRole: '小学教师',
  },
  i18n: {
    localeTable: {
      zh_CN: {
        'is required': '{{field}} 是必填项',
        'invalid format': '{{field}} 格式不正确',
      },
    },
  },
  cos: {
    secretId: 'AKIDJRL2VjjU2JL7g06xZy84lErGSREl391e', // 固定密钥
    secretKey: 'ShhjY9WgfR1OuKVY1KPJ0SK0SgjY9e0B', // 固定密钥
    proxy: '',
    host: 'sts.tencentcloudapi.com', // 域名，非必须，默认为 sts.tencentcloudapi.com
    // endpoint: 'sts.internal.tencentcloudapi.com', // 域名，非必须，与host二选一，默认为 sts.tencentcloudapi.com
    durationSeconds: 1800, // 密钥有效期
    // 放行判断相关参数
    AppID: '1301720845',
    region: 'ap-nanjing', // 换成 bucket 所在地区
    bucket: 'ysp-uploader-1301720845', // 换成你的 bucket
    allowPrefix: 'homework/*', // 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径，例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
  },
  sso: {
    AppID: 'xr2euke2',
    AppSecret: 'cn37ootpkk7',
    Host: 'http://sso.xdpb.top',
    state: 'sso2025',
  },
  axios: {
    clients: {
      sso: {
        baseURL: 'http://sso.xdpb.top/api',
        timeout: 1000 * 5,
      },
      carry: {
        baseURL: 'http://snedu.tpddns.cn:1688/xian_zhjy',
        timeout: 1000 * 10,
      },
    },
  },
  busboy: {
    // mode: UploadMode, 默认为file，即上传到服务器临时目录，可以配置为 stream
    mode: 'file',
    // whitelist: string[]，文件扩展名白名单
    whitelist: [...uploadWhiteList, '.docx', '.xlsx'],
    // tmpdir: string，上传的文件临时存储路径
    tmpdir: join(tmpdir(), 'midway-busboy-files'),
    // cleanTimeout: number，上传的文件在临时目录中多久之后自动删除，默认为 5 分钟
    cleanTimeout: 5 * 60 * 1000,
    // base64: boolean，设置原始body是否是base64格式，默认为false，一般用于腾讯云的兼容
    base64: false,
    // 仅在设置的以下路径时允许文件上传解析
    // match: ['/file/upload_single/local', '/file/upload_single/temp'],
    limits: {
      fileSize: 1024 * 1024 * 20, // 文件最大上传大小 20MB
    },
    defParamCharset: 'utf8', // 默认参数编码格式
  },
  bodyParser: {
    enableTypes: ['json', 'form', 'text'],
    jsonLimit: '10mb',
    formLimit: '10mb',
    textLimit: '10mb',
    xmlLimit: '10mb',
  },
  view: {
    defaultExtension: '.ejs',
    mapping: {
      '.ejs': 'ejs',
    },
    rootDir: {
      default: join(__dirname, './templates'),
    },
  },
  fileTransform: {
    baseURL: 'http://localhost:3131',
    parseWord: '/word/parse',
    exportWord: '/word-export/export/local',
  },
  upload: {
    enable: true,
    mode: 'file',
    fileSize: '10mb', // 文件大小限制
    whitelist: [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.pdf',
      '.docx',
      '.xlsx',
      '.html',
    ], // 允许上传的文件类型
    // dir: join(__dirname, '../public/temp'), //正式文件存储路径，不会自动清理
    tmpdir: join(__dirname, '../public/temp'), // 临时文件存储路径，会自动清理
  },
} as MidwayConfig;
