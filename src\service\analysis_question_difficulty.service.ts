import { Inject, Provide } from '@midwayjs/core';
import { AnalysisQuestionDifficulty, Enterprise, Semester } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { col, fn } from 'sequelize';
import { SchoolQuestionService } from './school_question.service';

@Provide()
export class AnalysisQuestionDifficultyService extends BaseService<AnalysisQuestionDifficulty> {
  @Inject()
  ctx: Context;
  @Inject()
  SchoolQuestionService: SchoolQuestionService;

  constructor() {
    super('试题难度分析');
  }
  getModel = () => {
    return AnalysisQuestionDifficulty;
  };

  async statistic(query: any) {
    const { semester_code, city_code, area_code, grade_section_code } = query;

    const queryOption = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (city_code) {
      queryOption['city_code'] = city_code;
    }
    if (area_code) {
      queryOption['area_code'] = area_code;
    }
    if (grade_section_code) {
      queryOption['grade_section_code'] = grade_section_code;
    }

    const res = await AnalysisQuestionDifficulty.findAll({
      where: queryOption,
      attributes: [
        'grade_section_name',
        'subject_id',
        'subject',
        'difficulty_name',
        [fn('SUM', col('difficulty_number')), 'total_difficulty_number'],
      ],
      group: ['grade_section_name', 'subject_id', 'subject', 'difficulty_name'],
    });
    return res;
  }

  /**
   * 按要求转换数据为树结构
   * @param data
   */
  async transformData(data) {
    // 使用一个Map来按科目分组
    const subjectMap = new Map();
    // 遍历原始数据并转换格式
    for (const item of data) {
      const {
        subject_id,
        subject,
        difficulty_name,
        total_question_difficulty_number,
      } = item.toJSON();

      // 如果科目不存在于Map中，则创建一个新的条目
      if (!subjectMap.has(item.subject_id)) {
        subjectMap.set(item.subject_id, {
          subject_name: subject,
          subject_id: subject_id,
          children: [],
        });
      }

      // 获取对应的科目对象
      const subjectEntry = subjectMap.get(item.subject_id);

      // 将当前条目添加到对应的科目对象的children数组中
      subjectEntry.children.push({
        difficulty_name: difficulty_name,
        total_question_difficulty_number: total_question_difficulty_number,
      });
    }

    // 将Map转换为数组
    return Array.from(subjectMap.values());
  }

  /**
   *  计算当前学年学期的题目难度数据
   * @returns 获取当前学年学期数据
   */
  async calculateDifficulty() {
    // 获取当前学年学期数据
    const semester = await Semester.findOne({ where: { status: 1 } });
    if (!semester) {
      return;
    }
    // 获取有区域数据的所有学校
    const schools = await Enterprise.findAll({
      where: { type: 'SCHOOL' },
      attributes: [
        'code',
        'area',
        'area_name',
        'city',
        'city_name',
        'school_system',
        'province',
        'province_name',
      ],
    });
    if (!schools.length) {
      return;
    }
    // 遍历学校
    const schoolCodes = schools.map(item => item.code);
    // 查询学校试题表数据
    const schoolQuestions =
      await this.SchoolQuestionService.findQuestionsForStatic({
        enterpriseCode: {
          $in: schoolCodes,
        },
      });
    // 将试题难度数据插入到试题难度表中 粒度到学科
    const insertArr = schoolQuestions
      .map(schoolQuestion => {
        const {
          enterpriseCode,
          subjectId,
          subjectName,
          gradeSectionCode,
          gradeSectionName,
          difficultyCode,
          difficultyName,
          count,
        } = schoolQuestion;
        const enterpriseInfo = schools.find(
          school => school.code === enterpriseCode
        );
        if (!enterpriseInfo) {
          return null;
        }
        return {
          subject_id: subjectId,
          subject: subjectName,
          difficulty_name: difficultyName,
          difficulty_number: count,
          difficulty_code: difficultyCode,
          grade_section_code: gradeSectionCode,
          grade_section_name: gradeSectionName,
          area_name: enterpriseInfo ? enterpriseInfo.area_name : '',
          city_name: enterpriseInfo ? enterpriseInfo.city_name : '',
          area_code: enterpriseInfo ? enterpriseInfo.area : '',
          city_code: enterpriseInfo ? enterpriseInfo.city : '',
          province_name: enterpriseInfo ? enterpriseInfo.province_name : '',
          province_code: enterpriseInfo ? enterpriseInfo.province : '',
          enterprise_code: enterpriseInfo ? enterpriseInfo.code : '',
          enterprise_name: enterpriseInfo ? enterpriseInfo.name : '',
          school_system_code: enterpriseInfo
            ? enterpriseInfo.school_system
            : '',
          school_system_name: enterpriseInfo
            ? enterpriseInfo.school_system_name
            : '',
          semester_code: semester ? semester.code : '',
          semester_name: semester ? semester.term : '',
        };
      })
      .filter(i => i);
    return await this.batchCreate(insertArr);
  }

  /**
   * 自动计算当前学年学期的题目难度
   * @param semester 学年学期数据
   * @param schools 学校数据
   */
  async autoCalculateDifficulty(semester, schools) {
    // 遍历学校
    const schoolCodes = schools.map(item => item.code);
    // 查询学校试题表数据
    const schoolQuestions =
      await this.SchoolQuestionService.findQuestionsForStatic({
        enterpriseCode: {
          $in: schoolCodes,
        },
      });

    const dataToCreate = []; // 用于存储需要创建的记录
    const dataToUpdate = []; // 用于存储需要更新的记录

    for (const schoolQuestion of schoolQuestions) {
      const {
        enterpriseCode,
        subjectId,
        subjectName,
        gradeSectionCode,
        gradeSectionName,
        difficultyCode,
        difficultyName,
        count,
      } = schoolQuestion;

      const enterpriseInfo = schools.find(
        school => school.code === enterpriseCode
      );
      if (!enterpriseInfo) {
        return null;
      }

      // 查询当前学年学期是否存在记录
      const existingRecord = await AnalysisQuestionDifficulty.findOne({
        where: {
          subject_id: subjectId,
          subject: subjectName,
          difficulty_name: difficultyName,
          grade_section_code: gradeSectionCode,
          semester_code: semester ? semester.code : '',
          enterprise_code: enterpriseInfo ? enterpriseInfo.code : '',
        },
      });

      const record = {
        semester_code: semester ? semester.code : '', // 学年学期code
        semester_name: semester ? semester.term : '', // 学年学期
        province_code: enterpriseInfo ? enterpriseInfo.province : '', // 省code
        province_name: enterpriseInfo ? enterpriseInfo.province_name : '', // 省
        city_code: enterpriseInfo ? enterpriseInfo.city : '', // 市code
        city_name: enterpriseInfo ? enterpriseInfo.city_name : '', // 市
        area_code: enterpriseInfo ? enterpriseInfo.area : '', // 区code
        area_name: enterpriseInfo ? enterpriseInfo.area_name : '', // 区
        school_system_code: enterpriseInfo ? enterpriseInfo.school_system : '', // 学制code
        school_system_name: enterpriseInfo
          ? enterpriseInfo.school_system_name
          : '', // 学制
        grade_section_code: gradeSectionCode, // 年级
        grade_section_name: gradeSectionName, // 年级
        enterprise_code: enterpriseInfo ? enterpriseInfo.code : '', // 学校code
        enterprise_name: enterpriseInfo ? enterpriseInfo.name : '', // 学校
        subject_id: subjectId, // 学科id
        subject: subjectName, // 学科
        difficulty_name: difficultyName, // 难度
        difficulty_code: difficultyCode, // 难度code
        difficulty_number: count, // 难度数量
      };

      // 如果存在记录,则更新,否则创建
      if (existingRecord) {
        dataToUpdate.push({
          id: existingRecord.id,
          difficulty_number: count,
        });
      } else {
        dataToCreate.push(record);
      }
    }

    // 批量创建或更新数据
    if (dataToCreate.length) {
      await this.batchCreteOrUpdate(dataToCreate, 'create');
    }
    if (dataToUpdate.length) {
      await this.batchCreteOrUpdate(dataToUpdate, 'update');
    }
  }
}
