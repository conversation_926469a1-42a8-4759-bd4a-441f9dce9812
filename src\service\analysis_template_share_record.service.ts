import { Inject, Provide } from '@midwayjs/core';
import {
  AnalysisTemplateShareRecord,
  ClassWork,
  ComplianceDetectionDesign,
  ComplianceDetectionTemplate,
  LessonWorkDesign,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';
import { SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP } from '../common/Constants';

@Provide()
export class AnalysisTemplateShareRecordService extends BaseService<AnalysisTemplateShareRecord> {
  @Inject()
  ctx: Context;

  constructor() {
    super('范本共享记录表');
  }
  getModel = () => {
    return AnalysisTemplateShareRecord;
  };

  /**
   * 自动汇总各个学校的范本共享次数
   * @param semester 当前学年学期
   * @param enterpirses 所有学校信息
   */
  async autoSumaryTemplateShareCount(semester, enterprises) {
    // 格式化当前学年学期的时间范围 方便后续查询使用
    const startTime = new Date(semester.start_date);
    const endTime = new Date(semester.end_date);

    const recordsToCreate = []; // 需要创建的记录数组
    const recordsToUpdate = []; // 需要更新的记录数组

    // 遍历每一个学校，计算该学校下的范本共享次数 范本包括课时范本和达标范本
    for (const enterprise of enterprises) {
      if (!enterprise.school_system) {
        continue;
      }
      // 根据学校的学制信息 拆分不同学段的数据入库
      const gradeSection =
        SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP[enterprise.school_system];
      if (!gradeSection) {
        continue; // 没有查到对应的学段 直接跳过该学校的数据统计
      }

      for (const gradeSectionItem of gradeSection) {
        const baseQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: enterprise.id,
          grade_section_code: gradeSectionItem.code,
          is_share: true, // 只查询共享的
        };

        // 查询共享范本ID集合
        const [classWork, detectionTemplate] = await Promise.all([
          ClassWork.findAll({ where: baseQuery, attributes: ['id'] }),
          ComplianceDetectionTemplate.findAll({
            where: baseQuery,
            attributes: ['id'],
          }),
        ]);

        const classWorkIdArr = classWork.map(v => v.id);
        const detectionTemplateIdArr = detectionTemplate.map(v => v.id);

        // 统计共享次数
        const sharedQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: { [Op.ne]: enterprise.id }, // 不查询当前学校自己的共享次数
        };

        // 查询范本共享次数
        const [classWorkCount, detectionTemplateCount] = await Promise.all([
          ClassWork.count({
            where: {
              ...sharedQuery,
              template_id: { [Op.in]: classWorkIdArr },
            },
          }),
          ComplianceDetectionTemplate.count({
            where: {
              ...sharedQuery,
              template_id: { [Op.in]: detectionTemplateIdArr },
            },
          }),
        ]);

        // 查询作业设计引用次数
        const [designCount, detectionDesignCount] = await Promise.all([
          LessonWorkDesign.count({
            where: {
              ...sharedQuery,
              class_work_id: { [Op.in]: classWorkIdArr },
            },
          }),
          ComplianceDetectionDesign.count({
            where: {
              ...sharedQuery,
              template_id: { [Op.in]: detectionTemplateIdArr },
            },
          }),
        ]);

        // 范本共享次数 = 课时范本+达标范本+课时作业设计+达标作业设计引用总和
        const totalShareCount =
          classWorkCount +
          detectionTemplateCount +
          designCount +
          detectionDesignCount;

        // 查询当前学校在当前学期、学段的共享记录 若存在则更新 若不存在则创建
        const shareRecord = await AnalysisTemplateShareRecord.findOne({
          where: {
            enterprise_code: enterprise.code,
            semester_code: semester.code,
            grade_section_code: gradeSectionItem.code,
          },
        });

        const shareData = {
          semester_code: semester.code, // 学年学期
          semester_name: semester.term.toString(), // 学年学期名称
          province_code: enterprise.province, // 省份编码
          province_name: enterprise.province_name, // 省份名称
          city_code: enterprise.city, // 市编码
          city_name: enterprise.city_name, // 市名称
          area_code: enterprise.area, // 区编码
          area_name: enterprise.area_name, // 区名称
          school_system_code: enterprise.school_system, // 学制
          school_system_name: enterprise.school_system_name, // 学制名称
          grade_section_code: gradeSectionItem.code, // 学段编码
          grade_section_name: gradeSectionItem.name, // 学段名称
          enterprise_code: enterprise.code, // 学校编码
          enterprise_name: enterprise.name, // 学校名称
          template_cite_number: totalShareCount, // 共享次数
        };

        if (shareRecord) {
          recordsToUpdate.push({
            id: shareRecord.id,
            template_cite_number: totalShareCount,
          });
        } else {
          recordsToCreate.push(shareData);
        }
      }
    }

    // 批量创建或更新数据
    if (recordsToCreate.length > 0) {
      await this.batchCreteOrUpdate(recordsToCreate, 'create');
    }
    if (recordsToUpdate.length > 0) {
      await this.batchCreteOrUpdate(recordsToUpdate, 'update');
    }
  }
}
