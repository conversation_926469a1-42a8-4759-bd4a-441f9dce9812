import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface AnalysisTeacherWorkRecordInterface {
  /** id */
  id: number;
  /** 学期编码 */
  semester_code?: string;
  /** 学期名称 */
  semester_name?: string;
  /** 学段编码 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 企业编码 */
  enterprise_code?: string;
  /** 企业名称 */
  enterprise_name?: string;
  /** 年级编码 */
  grade_code?: string;
  /** 年级名称 */
  grade_name?: string;
  /** 科目id */
  subject_id?: number;
  /** 科目名称 */
  subject?: string;
  /** 教师id */
  teacher_id?: number;
  /** 教师名称 */
  teacher_name?: string;
  /** 作业数量 */
  homework_number?: number;
  /** 平均时长 */
  average_time?: string;
  /** 建议时长 */
  suggested_time?: string;
  /** 试题数量 */
  question_number?: number;
}

@Table({
  tableName: 'analysis_teacher_work_record',
  timestamps: true,
  comment: '教师作业记录表',
})
export class AnalysisTeacherWorkRecord
  extends Model<AnalysisTeacherWorkRecordInterface>
  implements AnalysisTeacherWorkRecordInterface
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期编码',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期名称',
  })
  semester_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段编码',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '企业编码',
  })
  enterprise_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '企业名称',
  })
  enterprise_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '年级编码',
  })
  grade_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '年级名称',
  })
  grade_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '科目id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '科目名称',
  })
  subject?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '教师id',
  })
  teacher_id?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '教师名称',
  })
  teacher_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '作业数量',
  })
  homework_number?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '平均时长',
  })
  average_time?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '建议时长',
  })
  suggested_time?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '试题数量',
  })
  question_number?: number;
}
