{"name": "homework-design", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/axios": "^3.20.2", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/bullmq": "^3.20.5", "@midwayjs/busboy": "^3.20.3", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.20.2", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/redis": "^3.20.4", "@midwayjs/sequelize": "^3.20.2", "@midwayjs/typegoose": "^3.0.0", "@midwayjs/upload": "^3.20.5", "@midwayjs/validate": "^3.12.0", "@midwayjs/view-ejs": "^3.20.5", "@typegoose/typegoose": "^8.0.0", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cos-nodejs-sdk-v5": "^2.14.6", "dayjs": "^1.11.13", "ejs": "^3.1.10", "ioredis": "^5.6.1", "lodash": "^4.17.21", "mongoose": "^5.13.3", "mysql2": "^3.12.0", "node-xlsx": "^0.24.0", "puppeteer": "^24.10.0", "qcloud-cos-sts": "^3.1.1", "sequelize": "^6.37.5", "sequelize-typescript": "^2.1.6"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/ejs": "^3.1.5", "@types/jest": "^29.2.0", "@types/lodash": "^4.17.16", "@types/node": "14", "cross-env": "^6.0.0", "dayjs": "1.11.13", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}