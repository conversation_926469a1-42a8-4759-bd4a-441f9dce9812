import { Controller, Post, Body, Inject, Get, Query } from '@midwayjs/core';
import { AuthService } from '../service/auth.service';
import {
  LOCAL_LOGIN_REQUEST_DTO,
  LOCAL_REGISTER_REQUEST_DTO,
  REFRESH_TOKEN_REQUEST_DTO,
} from '../dto/auth';

@Controller('/auth')
export class AuthController {
  @Inject()
  authService: AuthService;

  @Post('/login', { summary: '本地账密登录' })
  async login(@Body() loginData: LOCAL_LOGIN_REQUEST_DTO) {
    const result = await this.authService.login(
      loginData.username,
      loginData.password
    );
    return result;
  }

  @Post('/register', { summary: '用户注册' })
  async register(@Body() registerData: LOCAL_REGISTER_REQUEST_DTO) {
    const result = await this.authService.register(registerData);
    return result;
  }

  @Post('/refresh', { summary: '刷新token' })
  async refresh(@Body() refreshData: REFRESH_TOKEN_REQUEST_DTO) {
    const result = await this.authService.refresh(refreshData.refreshToken);
    return result;
  }

  @Get('/userinfo', { summary: '获取用户信息' })
  async userinfo(@Query('access_token') token: string) {
    const userInfo = await this.authService.getUserInfo(token);
    return userInfo;
  }
}
