import { IProcessor, Processor } from '@midwayjs/bullmq';
import { FORMAT, Inject } from '@midwayjs/core';
import { AnalysisHomeworkRecordService } from '../service/analysis_homework_record.service';
import { join } from 'path';
import * as fs from 'fs';

@Processor('every_day_task', {
  repeat: {
    // 设置定时任务每天零点执行一次
    pattern: FORMAT.CRONTAB.EVERY_DAY,
    // pattern: FORMAT.CRONTAB.EVERY_MINUTE,
  },
})
export class TestProcessor implements IProcessor {
  @Inject()
  analysisHomeworkRecordService: AnalysisHomeworkRecordService;

  async execute() {
    console.log('定时任务启动', new Date().toLocaleString());
    // 定时汇总作业记录数据
    await this.analysisHomeworkRecordService.autoSummary();

    // 定时清理临时文件夹下的临时文件
    const tempDir = join(__dirname, '../public/temp');
    if (fs.existsSync(tempDir)) {
      const files = fs.readdirSync(tempDir);

      for (const file of files) {
        const filePath = join(tempDir, file);
        fs.unlinkSync(filePath); // 删除文件
        console.log(`已删除临时文件：${filePath}`);
      }
      console.log(`已完成对 ${tempDir} 目录的清理`);
    } else {
      console.warn(`临时目录不存在：${tempDir}`);
    }
  }
}
