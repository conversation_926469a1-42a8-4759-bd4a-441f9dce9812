import { Inject, Provide } from '@midwayjs/core';
import {
  AnalysisHomeworkRecord,
  CityHomeworkRequirementDuration,
  Enterprise,
  Semester,
  LessonWorkDesign,
  LessonWorkDesignDetail,
  ComplianceDetectionDesignDetail,
  ComplianceDetectionDesign,
  Subject,
  User,
  Role,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op, col, fn, literal } from 'sequelize';
import * as dayjs from 'dayjs';
import {
  SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP,
  GRADE_SECTION_TO_GRADE_MAP,
} from '../common/Constants';
import { QuestionService } from './question.service';
import { SchoolQuestions, SystemQuestions } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { AnalysisTemplateShareRecordService } from './analysis_template_share_record.service';
import { AnalysisQuestionDifficultyService } from './analysis_question_difficulty.service';
import { AnalysisTeacherWorkRecordService } from './analysis_teacher_work_record.service';
import { Util } from '../common/Util';

@Provide()
export class AnalysisHomeworkRecordService extends BaseService<AnalysisHomeworkRecord> {
  @Inject()
  ctx: Context;

  @Inject()
  questionService: QuestionService;

  @InjectEntityModel(SchoolQuestions)
  schoolQuestions: ReturnModelType<typeof SchoolQuestions>;

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  @Inject()
  analysisTemplateShareRecordService: AnalysisTemplateShareRecordService;

  @Inject()
  analysisQuestionDifficultyService: AnalysisQuestionDifficultyService;

  @Inject()
  analysisTeacherWorkRecordService: AnalysisTeacherWorkRecordService;

  constructor() {
    super('作业分析记录表');
  }
  getModel = () => {
    return AnalysisHomeworkRecord;
  };

  /**
   * 按条件汇总学校总数、作业总数、平均时长、试题数量等数据
   * @param query 请求参数
   * @returns
   */
  async statistic(query) {
    const { semester_code, city_code, area_code } = query;
    // 组装查询条件
    const queryOption: any = {};
    if (semester_code) {
      queryOption.semester_code = semester_code;
    }
    if (city_code) {
      queryOption.city_code = city_code;
    }
    if (area_code) {
      queryOption.area_code = area_code;
    }

    const currentMonth = `${dayjs().month() + 1}月`; // month() 返回 0-11，所以要 +1
    queryOption.month = currentMonth;
    // 获取当前月份的 学校总数、作业总数、平均时长、试题数量
    const currentMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('COUNT', fn('DISTINCT', col('enterprise_code'))), 'school_count'],
        // 小学 学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '小学' THEN enterprise_code END"
              )
            )
          ),
          'primary_school_count',
        ],
        // 初中  学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '初中' THEN enterprise_code END"
              )
            )
          ),
          'middle_school_count',
        ],
        // 高中  学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '高中' THEN enterprise_code END"
              )
            )
          ),
          'high_school_count',
        ],
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 查询当月按学段统计的 作业次数、平均作业时长、全市试题总量
    const currentMonthStageInfo = await AnalysisHomeworkRecord.findAll({
      attributes: [
        'grade_section_name',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'avg_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'avg_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      where: queryOption,
      group: ['grade_section_name'],
      order: [
        [
          literal(`
            CASE grade_section_name
              WHEN '小学' THEN 1
              WHEN '初中' THEN 2
              WHEN '高中' THEN 3
              ELSE 4
            END
          `),
          'ASC',
        ],
      ],
    });

    const lastMonth = `${dayjs().subtract(1, 'month').month() + 1}月`;
    queryOption.month = lastMonth;
    // 获取上月的 学校总数、作业总数、平均时长、试题数量
    const lastMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('COUNT', fn('DISTINCT', col('enterprise_code'))), 'school_count'],
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 查询上月按学段统计 作业次数、平均作业时长、全市试题总量
    const lastMonthStageInfo = await AnalysisHomeworkRecord.findAll({
      attributes: [
        'grade_section_name',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'avg_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'avg_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      where: queryOption,
      group: ['grade_section_name'],
      order: [
        [
          literal(`
            CASE grade_section_name
              WHEN '小学' THEN 1
              WHEN '初中' THEN 2
              WHEN '高中' THEN 3
              ELSE 4
            END
          `),
          'ASC',
        ],
      ],
    });

    // 计算全市作业次数较上月增加的百分比
    const currentMonthTotalHomework = Number(
      currentMonthRes[0].get('total_homework_number') || 0
    );
    const lastMonthTotalHomework = Number(
      lastMonthRes[0].get('total_homework_number') || 0
    );
    const homework_number_last_month = Util.calculateGrowthRate(
      currentMonthTotalHomework,
      lastMonthTotalHomework
    );

    // 计算全市作业时长较上月增加的百分比
    const currentMonthTotalAverageTime = Number(
      currentMonthRes[0].get('total_average_time') || 0
    );
    const lastMonthTotalAverageTime = Number(
      lastMonthRes[0].get('total_average_time') || 0
    );
    const average_time_last_month = Util.calculateGrowthRate(
      currentMonthTotalAverageTime,
      lastMonthTotalAverageTime
    );

    // 计算全市试题数量较上月增加的百分比
    const currentMonthTotalQuestionNumber = Number(
      currentMonthRes[0].get('total_question_number') || 0
    );
    const lastMonthTotalQuestionNumber = Number(
      lastMonthRes[0].get('total_question_number') || 0
    );
    const question_number_last_month = Util.calculateGrowthRate(
      currentMonthTotalQuestionNumber,
      lastMonthTotalQuestionNumber
    );

    // 计算各阶段作业次数较上月增加的百分比
    const growthRates = [];
    let growthObj = {};
    currentMonthStageInfo.forEach(currentItem => {
      const gradeSectionName = currentItem.get('grade_section_name');
      const lastItem = lastMonthStageInfo.find(
        item => item.get('grade_section_name') === gradeSectionName
      );

      // 转换为数字
      const currentHomework = Number(
        currentItem.get('total_homework_number') || 0
      );
      const lastHomework = Number(lastItem?.get('total_homework_number') ?? 0);
      const currentAverageTime = Number(
        currentItem.get('avg_average_time') || 0
      );
      const lastAverageTime = Number(lastItem?.get('avg_average_time') ?? 0);

      const currentQuestion = Number(
        currentItem.get('total_question_number') || 0
      );
      const lastQuestion = Number(lastItem?.get('total_question_number') ?? 0);

      const homeworkGrowth = Util.calculateGrowthRate(
        currentHomework,
        lastHomework
      );

      const averageTimeGrowth = Util.calculateGrowthRate(
        currentAverageTime,
        lastAverageTime
      );

      const questionGrowth = Util.calculateGrowthRate(
        currentQuestion,
        lastQuestion
      );

      growthRates.push({
        grade_section_name: gradeSectionName,
        homework_growth_rate: homeworkGrowth !== 0 ? `${homeworkGrowth}%` : 0,
        average_time_growth_rate:
          averageTimeGrowth !== 0 ? `${averageTimeGrowth}%` : 0,
        question_growth_rate: questionGrowth !== 0 ? `${questionGrowth}%` : 0,
      });

      if (growthRates.length > 0) {
        growthObj = growthRates.reduce(
          (acc, item) => {
            const section = item.grade_section_name;
            acc.homework_growth_rate[section] = item.homework_growth_rate;
            acc.average_time_growth_rate[section] =
              item.average_time_growth_rate;
            acc.question_growth_rate[section] = item.question_growth_rate;
            return acc;
          },
          {
            homework_growth_rate: {},
            average_time_growth_rate: {},
            question_growth_rate: {},
          }
        );
      }
    });

    return {
      school_count: currentMonthRes[0].get('school_count'),
      school_count_primary: currentMonthRes[0].get('primary_school_count'),
      school_count_middle: currentMonthRes[0].get('middle_school_count'),
      school_count_high: currentMonthRes[0].get('high_school_count'),
      total_homework_number: currentMonthTotalHomework,
      homework_number_last_month:
        homework_number_last_month !== 0 ? `${homework_number_last_month}%` : 0,
      total_average_time: currentMonthTotalAverageTime,
      average_time_last_month:
        average_time_last_month !== 0 ? `${average_time_last_month}%` : 0,
      total_question_number: currentMonthTotalQuestionNumber,
      question_number_last_month:
        question_number_last_month !== 0 ? `${question_number_last_month}%` : 0,
      growthObj,
    };
  }

  /**
   * 根据条件获取不同区县下的作业汇总数据
   */
  async statisticHomeworkAnalysis(query) {
    const {
      semester_code,
      city_code,
      area_code,
      grade_section_code,
      grade_code,
      subject_id,
      enterprise_code,
    } = query;
    // 组装查询条件
    const queryOption: any = {};
    // 默认查询当前月份数据, 每月的数据都是最新的
    queryOption.month = `${dayjs().month() + 1}月`;
    if (semester_code) {
      queryOption.semester_code = semester_code;
    }
    if (city_code) {
      queryOption.city_code = city_code;
    }
    if (area_code) {
      queryOption.area_code = area_code;
    }
    if (grade_section_code) {
      queryOption.grade_section_code = grade_section_code;
    }
    if (grade_code) {
      queryOption.grade_code = grade_code;
    }
    if (subject_id) {
      queryOption.subject_id = subject_id;
    }
    if (enterprise_code) {
      queryOption.enterprise_code = enterprise_code;
    }

    // 如果年级code存在，则区县结果需要对应学科建议时长
    if (grade_code && !area_code && !enterprise_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'grade_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'grade_name',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    // 如果年级code 市code存在，则学校结果需要对应学科建议时长
    if (grade_code && city_code && !enterprise_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'enterprise_code',
          'enterprise_name',
          'grade_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'enterprise_code',
          'enterprise_name',
          'grade_name',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    // 如果市级code存在，区县code不存在，说明是统计市级下的区县数据
    if (city_code && !area_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'grade_section_name',
          'subject',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'grade_section_name',
          'subject',
        ],
        order: [
          [
            literal(`
              CASE grade_section_name
                WHEN '小学' THEN 1
                WHEN '初中' THEN 2
                WHEN '高中' THEN 3
                ELSE 4
              END
            `),
            'ASC',
          ],
        ],
      });
      return res;
    }

    // 如果city_code和area_code存在，说明是统计区县下的学校数据
    if (city_code && area_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'enterprise_code',
          'enterprise_name',
          'grade_section_name',
          'subject',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'enterprise_code',
          'enterprise_name',
          'grade_section_name',
          'subject',
        ],
        order: [
          [
            literal(`
              CASE grade_section_name
                WHEN '小学' THEN 1
                WHEN '初中' THEN 2
                WHEN '高中' THEN 3
                ELSE 4
              END
            `),
            'ASC',
          ],
        ],
      });
      return res;
    }

    // 如果学校code存在，说明是统计学校下各个年级的数据
    if (enterprise_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'grade_code',
          'grade_name',
          'grade_section_name',
          'subject',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [
            fn('ROUND', fn('AVG', col('average_time')), 2),
            'total_average_time',
          ],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: ['grade_code', 'grade_name', 'grade_section_name', 'subject'],
        order: [
          [
            literal(`
              CASE grade_section_name
                WHEN '小学' THEN 1
                WHEN '初中' THEN 2
                WHEN '高中' THEN 3
                ELSE 4
              END
            `),
            'ASC',
          ],
        ],
      });
      return res;
    }
  }

  /**
   * 按月统计试题数量和作业数量
   * @param query
   */
  async statisticMonth(query) {
    const {
      semester_code,
      city_code,
      area_code,
      grade_section_code,
      enterprise_code,
    } = query;

    const queryOption = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (city_code) {
      queryOption['city_code'] = city_code;
    }
    if (area_code) {
      queryOption['area_code'] = area_code;
    }
    if (grade_section_code) {
      queryOption['grade_section_code'] = grade_section_code;
    }
    if (enterprise_code) {
      queryOption['enterprise_code'] = enterprise_code;
    }

    const res = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        'month',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      group: ['month'],
    });
    // 计算本月较上月的增长率情况
    const growthRates = await this.calculateGrowthRates(res);
    return growthRates;
  }

  /**
   * 计算各月份汇总数据之间的增长率
   * @param data 按月汇总数据
   */
  async calculateGrowthRates(data) {
    return data.map((item, index) => {
      const { month, total_homework_number, total_question_number } =
        item.toJSON();

      const currentHomework = Number(total_homework_number) || 0;
      const currentQuestion = Number(total_question_number) || 0;

      if (index === 0) {
        return {
          month,
          total_homework_number: currentHomework,
          homework_growth_rate: 0,
          total_question_number: currentQuestion,
          question_growth_rate: 0,
        };
      }

      const prevItem = data[index - 1]?.toJSON();
      const prevHomework = Number(prevItem?.total_homework_number) || 0;
      const prevQuestion = Number(prevItem?.total_question_number) || 0;

      const homeworkGrowthRate = Util.calculateGrowthRate(
        currentHomework,
        prevHomework
      );
      const questionGrowthRate = Util.calculateGrowthRate(
        currentQuestion,
        prevQuestion
      );

      return {
        month,
        total_homework_number: currentHomework,
        homework_growth_rate: Number(homeworkGrowthRate),
        total_question_number: currentQuestion,
        question_growth_rate: Number(questionGrowthRate),
      };
    });
  }

  /**
   * 查询学校教师总数、作业次数、平均作业时长、试题总数
   * @param query
   */
  async statisticBySchool(query) {
    const { semester_code, enterprise_code, grade_section_code } = query;

    const queryOption: any = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (enterprise_code) {
      queryOption['enterprise_code'] = enterprise_code;
    }
    if (grade_section_code) {
      queryOption['grade_section_code'] = grade_section_code;
    }

    // 获取该校教师总数
    const teacher_number = await User.count({
      include: [
        { model: Enterprise, where: { code: enterprise_code } },
        {
          model: Role,
          where: { name: { [Op.in]: ['小学教师', '初中教师', '高中教师'] } },
        },
      ],
    });

    const currentMonth = `${dayjs().month() + 1}月`; // month() 返回 0-11，所以要 +1
    queryOption.month = currentMonth;
    // 统计当月作业次数、平均作业时长、试题总量
    const currentMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    const lastMonth = `${dayjs().subtract(1, 'month').month() + 1}月`;
    queryOption.month = lastMonth;
    // 获取上月的 作业总数、平均时长、试题数量
    const lastMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 计算该校作业次数较上月增加的百分比
    const currentMonthTotalHomework = Number(
      currentMonthRes[0].get('total_homework_number') || 0
    );
    const lastMonthTotalHomework = Number(
      lastMonthRes[0].get('total_homework_number') || 0
    );
    const homework_number_last_month = Util.calculateGrowthRate(
      currentMonthTotalHomework,
      lastMonthTotalHomework
    );

    // 计算该校作业时长较上月增加的百分比
    const currentMonthTotalAverageTime = Number(
      currentMonthRes[0].get('total_average_time') || 0
    );
    const lastMonthTotalAverageTime = Number(
      lastMonthRes[0].get('total_average_time') || 0
    );
    const average_time_last_month = Util.calculateGrowthRate(
      currentMonthTotalAverageTime,
      lastMonthTotalAverageTime
    );

    // 计算该校试题数量较上月增加的百分比
    const currentMonthTotalQuestionNumber = Number(
      currentMonthRes[0].get('total_question_number') || 0
    );
    const lastMonthTotalQuestionNumber = Number(
      lastMonthRes[0].get('total_question_number') || 0
    );
    const question_number_last_month = Util.calculateGrowthRate(
      currentMonthTotalQuestionNumber,
      lastMonthTotalQuestionNumber
    );

    return {
      teacher_number: teacher_number, // 教师总数
      total_homework_number: currentMonthRes[0].get('total_homework_number'), //作业总次数
      total_average_time: currentMonthRes[0].get('total_average_time'), //平均作业时长
      total_question_number: currentMonthRes[0].get('total_question_number'), //试题总量
      homework_number_last_month:
        homework_number_last_month !== 0 ? `${homework_number_last_month}%` : 0, //作业次数较上月
      average_time_last_month:
        average_time_last_month !== 0 ? `${average_time_last_month}%` : 0, //平均作业时长较上月
      question_number_last_month:
        question_number_last_month !== 0 ? `${question_number_last_month}%` : 0, //试题数量较上月
    };
  }

  /**
   * 自动汇总统计数据 已放入定时任务汇总
   */
  async autoSummary() {
    // 获取对应的市级学科建议时长
    const suggestTime = await CityHomeworkRequirementDuration.findAll();
    // 获取当前激活的学年学期
    const semester = await Semester.findOne({ where: { status: 1 } });
    if (!semester) {
      return;
    }

    // 获取所有的学校信息
    const enterprises = await Enterprise.findAll({
      where: { type: 'SCHOOL' },
      attributes: [
        'id',
        'code',
        'name',
        'school_system',
        'school_system_name',
        'province',
        'province_name',
        'city',
        'city_name',
        'area',
        'area_name',
      ],
      include: [
        {
          model: User,
          where: { grade: { [Op.ne]: null }, subject_id: { [Op.ne]: null } },
          attributes: [
            'id',
            'nickname',
            'grade_section_code',
            'grade_section_name',
            'grade',
            'subject_id',
            'subject_name',
          ],
          required: false, //设置为false，表示 left join
        },
      ],
    });
    if (enterprises.length === 0) {
      return;
    }

    // 1.汇总学校作业次数、平均时长、建议时长、试题数量
    await this.autoSummaryHomeworkRecord(suggestTime, semester, enterprises);

    // 2.汇总学校范本共享次数
    await this.analysisTemplateShareRecordService.autoSumaryTemplateShareCount(
      semester,
      enterprises
    );

    // 3.汇总试题难度分析
    await this.analysisQuestionDifficultyService.autoCalculateDifficulty(
      semester,
      enterprises
    );

    // 4.汇总各科目教师作业次数、作业平均时长情况
    await this.analysisTeacherWorkRecordService.autoSummaryTeacherWorkRecord(
      suggestTime,
      semester,
      enterprises
    );
  }

  /**
   * 更新或创建对应的作业统计数据
   * @param suggestionTime 学科建议时长
   * @param semester 当前激活的学年学期
   * @param enterprises 所有学校信息
   */
  async autoSummaryHomeworkRecord(suggestionTime, semester, enterprises) {
    // 格式化当前学年学期的时间范围 方便后续查询使用
    const startTime = new Date(semester.start_date);
    const endTime = new Date(semester.end_date);

    const recordsToCreate = []; // 需要创建的数据
    const recordsToUpdate = []; // 需要更新的数据

    // 获取当前月份
    const currentMonth = `${dayjs().month() + 1}月`;
    // 遍历学校信息
    for (const enterprise of enterprises) {
      if (!enterprise.school_system) {
        continue;
      }

      // 根据学校的学制信息 获取对应的学段数组
      const gradeSection =
        SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP[enterprise.school_system];
      if (!gradeSection) {
        continue;
      }

      // 根据不同的学段，组装不同学段对应的年级、科目 平铺数据
      const gradeAndSubjectArr = [];
      for (const sectionInfo of gradeSection) {
        // 根据学段查询对应学科
        const subjectList = await Subject.findAll({
          where: {
            grade_section: sectionInfo.code,
          },
        });

        if (subjectList.length === 0) {
          continue;
        }

        // 根据学段获取对应的年级
        const gradeList = GRADE_SECTION_TO_GRADE_MAP[sectionInfo.code];
        if (gradeList.length === 0) {
          continue;
        }

        // 遍历对应年级、学科信息
        for (const grade of gradeList) {
          for (const subject of subjectList) {
            gradeAndSubjectArr.push({
              grade_section_code: sectionInfo.code,
              grade_section_name: sectionInfo.name,
              grade_code: grade.code,
              grade_name: grade.name,
              subject_id: subject.id,
              subject_name: subject.subject,
            });
          }
        }
      }

      // 遍历平铺的数据，分别查询对应学段、年级、学科的作业记录
      for (const subjectInfo of gradeAndSubjectArr) {
        const baseQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: enterprise.id,
          grade_section_code: subjectInfo.grade_section_code,
          grade_code: subjectInfo.grade_code,
          subject_id: subjectInfo.subject_id,
        };

        // 获取对应年级、学科 的 课时、达标范本详情，计算总时长 作业总数
        const [workDesignDetailList, detectionDesignDetailList] =
          await Promise.all([
            LessonWorkDesignDetail.findAll({
              include: [
                { model: LessonWorkDesign, where: baseQuery, attributes: [] },
              ],
              attributes: ['id', 'duration'],
            }),
            ComplianceDetectionDesignDetail.findAll({
              include: [
                {
                  model: ComplianceDetectionDesign,
                  where: baseQuery,
                  attributes: [],
                },
              ],
              attributes: ['id', 'duration'],
            }),
          ]);

        const questionDuration = workDesignDetailList.reduce((total, item) => {
          return total + item.duration;
        }, 0);

        const detectionQuestionDuration = detectionDesignDetailList.reduce(
          (total, item) => {
            return total + item.duration;
          },
          0
        );

        // 计算课时作业、达标作业总时长
        const totalDuration = questionDuration + detectionQuestionDuration;

        // 计算作业总数 课时作业详情数 + 达标作业详情数
        const homework_number =
          workDesignDetailList.length + detectionDesignDetailList.length;

        // 2.计算平均时长
        const average_time =
          homework_number > 0 && totalDuration > 0
            ? parseFloat((totalDuration / homework_number).toFixed(2))
            : 0;

        // 3.查询对应学段、年级、学科的试题数量
        const match = {};
        match['enterpriseCode'] = enterprise.code; // 企业编码
        match['grade.code'] = subjectInfo.grade_code; //年级code
        match['subject.id'] = subjectInfo.subject_id; //科目id
        match['gradeSection.code'] = subjectInfo.grade_section_code; //学段code
        match['createdAt'] = {
          $gte: startTime,
          $lte: endTime,
        }; //创建时间
        const question_number = await this.schoolQuestions
          .countDocuments(match)
          .exec();

        // 过滤出对应学段、年级、科目 的建议时长
        const suggestTime = suggestionTime.find(
          item =>
            item.grade_section_code === subjectInfo.grade_section_code &&
            item.subject_id === subjectInfo.subject_id &&
            item.grade_code === subjectInfo.grade_code
        );
        const suggested_time = suggestTime
          ? `${suggestTime.min_duration}-${suggestTime.max_duration}`
          : '';

        // 组装当前学校对应数据
        const schoolData = {
          semester_code: semester.code, // 学年学期code
          semester_name: semester.term.toString(), // 学年学期
          month: currentMonth, // 月份
          province_code: enterprise.province, // 省份code
          province_name: enterprise.province_name, // 省份名称
          city_code: enterprise.city, //市code
          city_name: enterprise.city_name, //市名称
          area_code: enterprise.area, //区code
          area_name: enterprise.area_name, //区名称
          school_system_code: enterprise.school_system, //学制code
          school_system_name: enterprise.school_system_name, //学制名称
          grade_section_code: subjectInfo.grade_section_code, //学段code
          grade_section_name: subjectInfo.grade_section_name, //学段名称
          enterprise_code: enterprise.code, //企业code
          enterprise_name: enterprise.name, //企业名称
          grade_code: subjectInfo.grade_code, //年级code
          grade_name: subjectInfo.grade_name, //年级名称
          subject_id: subjectInfo.subject_id, //科目id
          subject: subjectInfo.subject_name, //科目名称
          homework_number: homework_number, //作业数量
          average_time: average_time.toString(), //平均时长
          suggested_time: suggested_time, //建议时长
          question_number: question_number, //试题数量
        };

        // 查询当前学年学期对应月份、学校的对应学段、年级、学科是否存在记录
        const record = await AnalysisHomeworkRecord.findOne({
          where: {
            semester_code: semester.code,
            month: `${dayjs().month() + 1}月`,
            enterprise_code: enterprise.code,
            grade_section_code: subjectInfo.grade_section_code,
            grade_code: subjectInfo.grade_code,
            subject_id: subjectInfo.subject_id,
          },
        });

        if (record) {
          recordsToUpdate.push({
            id: record.id,
            homework_number, //作业数量
            average_time: average_time.toString(), //平均时长
            suggested_time: suggested_time, //建议时长
            question_number: question_number, //试题数量
          });
        } else {
          recordsToCreate.push(schoolData);
        }
      }
    }

    // 创建或者更新
    if (recordsToCreate.length > 0) {
      await this.batchCreteOrUpdate(recordsToCreate, 'create');
    }
    if (recordsToUpdate.length > 0) {
      await this.batchCreteOrUpdate(recordsToUpdate, 'update');
    }
  }
}
