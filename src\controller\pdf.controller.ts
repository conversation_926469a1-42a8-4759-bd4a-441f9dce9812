import { Inject, Controller, Post, Body, Files } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import puppeteer from 'puppeteer';
import { join } from 'path';
import * as fs from 'fs';
import { CustomError } from '../error/custom.error';

@Controller('/pdf')
export class PdfController {
  @Inject()
  ctx: Context;

  @Post('/upload_html', { summary: '上传HTML文件' })
  async uploadHtml(@Files() files) {
    // 检查文件是否存在
    if (!files || !Array.isArray(files) || files.length === 0) {
      throw new CustomError('未收到任何文件或文件格式不正确');
    }

    // 创建临时目录
    const tempDir = join(__dirname, '../public/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 处理上传文件
    const uploadedFiles = [];
    for (const file of files) {
      const newFilename = `${Date.now()}_${file.filename}`;
      const tempPath = join(tempDir, newFilename);

      // 移动文件到临时目录
      // fs.renameSync(file.data, tempPath); // 该方法会因磁盘目录不一致而导致报错
      fs.copyFileSync(file.data, tempPath);
      fs.unlinkSync(file.data); // 删除原文件

      uploadedFiles.push({
        filename: newFilename,
      });
    }
    // 目前下载pdf只会有一个文件，所有默认返回第一项
    return uploadedFiles[0];
  }

  @Post('/generate', { summary: '生成PDF文件' })
  async generatePDF(@Body() body: any) {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const { filename } = body;
    if (!filename) {
      throw new Error('缺少文件名');
    }

    try {
      const testPdfPath = join(__dirname, '../public/temp', filename);
      // 读取临时文件
      const testPdf = fs.readFileSync(testPdfPath, 'utf-8');

      const page = await browser.newPage();
      await page.setContent(testPdf);

      const pdfBuffer = await page.pdf({
        format: 'A4',
        scale: 1,
        printBackground: true,
        preferCSSPageSize: true,
      });
      // 如果文件存在，删除对应临时文件
      if (fs.existsSync(testPdfPath)) {
        fs.unlinkSync(testPdfPath); // 删除 HTML 临时文件
      }
      return pdfBuffer;
    } catch (error) {
      throw new CustomError('生成PDF文件失败');
    }
  }
}
