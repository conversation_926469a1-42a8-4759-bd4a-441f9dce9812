import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { DashboardService } from '../service/dashboard.service';

@Controller('/dashboard')
export class DashboardController {
  @Inject()
  ctx: Context;

  @Inject()
  service: DashboardService;

  @Get('/overview', { summary: '获取系统统计概览' })
  async getOverview(@Query() query: any) {
    return this.service.getOverview(query);
  }

  @Get('/enterprises/statistics', { summary: '获取企业统计数据' })
  async getEnterpriseStatistics(@Query() query: any) {
    return this.service.getEnterpriseStatistics(query);
  }

  @Get('/users/statistics', { summary: '获取用户统计数据' })
  async getUserStatistics(@Query() query: any) {
    return this.service.getUserStatistics(query);
  }
}
