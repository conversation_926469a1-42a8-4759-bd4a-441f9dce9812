import { Middleware, IMiddleware, Inject } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { Jwt, JwtService } from '@midwayjs/jwt';
import { UnauthorizedError } from '@midwayjs/core/dist/error/http';

/**
 * 对接口返回的数据统一包装
 */
@Middleware()
export class JWTMiddleware implements IMiddleware<Context, NextFunction> {
  @Inject()
  jwtService: JwtService;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 判断下有没有校验信息
      if (!ctx.headers['authorization']) {
        throw new UnauthorizedError('无效的令牌');
      }
      // 从 header 上获取校验信息
      const parts = ctx.get('authorization').trim().split(' ');

      if (parts.length !== 2) {
        throw new UnauthorizedError('无效的令牌');
      }
      const [scheme, token] = parts;
      if (/^Bearer$/i.test(scheme)) {
        try {
          //jwt.verify方法验证token是否有效
          const info = await this.jwtService.verify(token, {
            complete: true,
          });
          if (info) {
            //token有效
            ctx.state.user = (info as Jwt.Jwt).payload;
          }
        } catch (error) {
          console.log('=========', error);
          throw new UnauthorizedError('认证无效或过期，请重新登录');
        }
        await next();
      } else {
        throw new UnauthorizedError('无效的令牌');
      }
    };
  }

  ignore(ctx: Context): boolean {
    return (
      ctx.path.startsWith('/oauth') ||
      ctx.path.startsWith('/sso') ||
      ctx.path.startsWith('/auth')
    );
  }

  static getName(): string {
    return 'JWT_AUTH';
  }
}
