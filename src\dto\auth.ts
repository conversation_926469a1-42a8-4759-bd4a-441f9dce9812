import { Rule, RuleType } from '@midwayjs/validate';

export class SSO_CALLBACK_REQUEST_DTO {
  @Rule(RuleType.string().required().description('授权码'))
  code: string;
  @Rule(RuleType.string().required().description('重定向URI'))
  redirect_uri: string;
}

export class LOCAL_LOGIN_REQUEST_DTO {
  @Rule(RuleType.string().required().min(3).max(50).description('用户名'))
  username: string;
  @Rule(RuleType.string().required().min(1).max(100).description('密码'))
  password: string;
}

export class LOCAL_REGISTER_REQUEST_DTO {
  @Rule(RuleType.string().required().min(3).max(50).description('用户名'))
  username: string;
  @Rule(RuleType.string().required().min(1).max(100).description('密码'))
  password: string;
  @Rule(RuleType.string().optional().max(50).description('昵称'))
  nickname?: string;
  @Rule(RuleType.string().optional().email().description('邮箱'))
  email?: string;
  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^1[3-9]\d{9}$/)
      .description('手机号')
  )
  phone?: string;
}

export class REFRESH_TOKEN_REQUEST_DTO {
  @Rule(RuleType.string().required().description('刷新令牌'))
  refreshToken: string;
}
