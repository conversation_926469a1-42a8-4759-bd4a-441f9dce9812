import { Provide, Inject, Scope, ScopeEnum, App } from '@midwayjs/core';
import { JwtService } from '@midwayjs/jwt';
import * as bcrypt from 'bcryptjs';
import { User, Role, Enterprise } from '../entity';
import { CustomError } from '../error/custom.error';
import { Application } from '@midwayjs/koa';
import { LOCAL_REGISTER_REQUEST_DTO } from '../dto/auth';
import { FeatureService } from './feature.service';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class AuthService {
  @App()
  app: Application;

  @Inject()
  jwtService: JwtService;

  @Inject()
  featureService: FeatureService;

  async validateUser(username: string, password: string): Promise<User | null> {
    const user = await User.findOne({ where: { username } });
    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }
    return null;
  }

  async login(username: string, password: string) {
    const user = await User.findOne({
      where: { username },
    });
    if (!user) {
      throw new CustomError('无效的用户名');
    }
    if (!user.isActive) {
      throw new CustomError('账户已被禁用');
    }
    const passed = await bcrypt.compare(password, user.password);
    if (!passed) {
      throw new CustomError('密码错误');
    }

    const expiresIn = 7200; // 2小时
    // 生成 JWT，保持与SSO一致的payload结构
    const token = this.jwtService.signSync(
      { id: user.id, username: user.username },
      { expiresIn }
    );

    // 返回格式与SSO保持一致
    return {
      token,
      expiresIn,
    };
  }

  async refresh(refreshToken: string) {
    try {
      const decoded: any = this.jwtService.decodeSync(refreshToken);
      if (!decoded) {
        throw new CustomError('无效的刷新令牌');
      }

      // 验证用户是否仍然存在且激活
      const user = await User.findOne({
        where: { id: decoded.id, isActive: true },
      });
      if (!user) {
        throw new CustomError('用户不存在或已被禁用');
      }

      delete decoded.iat;
      delete decoded.exp;

      const expiresIn = 7200; // 2小时
      // 生成新的 JWT
      const newToken = this.jwtService.signSync(decoded, { expiresIn });
      return {
        token: newToken,
        expiresIn,
      };
    } catch (err) {
      console.error('refresh err: ', err);
      throw new CustomError('令牌刷新失败');
    }
  }

  async register(registerData: LOCAL_REGISTER_REQUEST_DTO) {
    const localAuthConfig = this.app.getConfig('localAuth');
    if (!localAuthConfig.allowRegister) {
      throw new CustomError('系统不允许用户注册');
    }

    // 检查用户名是否已存在
    const existingUser = await User.findOne({
      where: { username: registerData.username },
    });
    if (existingUser) {
      throw new CustomError('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (registerData.email) {
      const existingEmail = await User.findOne({
        where: { email: registerData.email },
      });
      if (existingEmail) {
        throw new CustomError('邮箱已被使用');
      }
    }

    // 检查手机号是否已存在
    if (registerData.phone) {
      const existingPhone = await User.findOne({
        where: { phone: registerData.phone },
      });
      if (existingPhone) {
        throw new CustomError('手机号已被使用');
      }
    }

    // 获取默认角色
    const defaultRole = await Role.findOne({
      where: { name: localAuthConfig.defaultRole },
    });
    if (!defaultRole) {
      throw new CustomError('系统配置错误：默认角色不存在');
    }

    // 创建用户
    const newUser = await User.create({
      username: registerData.username,
      password: registerData.password,
      nickname: registerData.nickname || registerData.username,
      email: registerData.email,
      phone: registerData.phone,
      isActive: true,
    });

    // 分配默认角色
    await newUser.$add('role', defaultRole);

    return {
      id: newUser.id,
      username: newUser.username,
      nickname: newUser.nickname,
      email: newUser.email,
      phone: newUser.phone,
    };
  }

  async getUserInfo(token: string) {
    // 解析JWT令牌获取用户信息，与SSO的userinfo接口保持一致
    const payload = this.jwtService.decodeSync(token) as any;
    const user = await User.findOne({
      where: {
        username: payload.username,
      },
      include: [
        {
          association: 'roles',
        },
        {
          model: Enterprise,
          attributes: [
            'code',
            'name',
            'school_system',
            'type',
            'province',
            'province_name',
            'city',
            'city_name',
            'area',
            'area_name',
          ],
        },
      ],
      attributes: {
        exclude: ['password'],
      },
    });

    if (!user) {
      throw new CustomError('获取用户信息失败');
    }

    const userInfo = user.toJSON();
    const { roles } = userInfo;
    const features = await this.featureService.getListByRoles(
      roles.map(item => item.id)
    );

    // 返回格式与SSO的userinfo接口完全一致
    return {
      ...userInfo,
      features,
    };
  }
}
